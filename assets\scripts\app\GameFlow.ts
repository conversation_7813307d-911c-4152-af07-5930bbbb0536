import { _decorator, Component, Node } from 'cc';
import { GameState } from '../enums/GameState';
import { GameManager } from '../core/GameManager';
import { SoundManager } from './SoundManager';
const { ccclass, property } = _decorator;

const mraid = window['mraid'] || null;

@ccclass('GameFlow')
export class GameFlow extends Component {
    public static isLoaded = false;
    public static instance: GameFlow = null;

    @property(GameManager)
    private gameManager: GameManager = null;

    private gameReady: boolean = false;

    protected onLoad(): void {
        if (GameFlow.instance === null) {
            GameFlow.instance = this;
            window['gameReady'] && window['gameReady']();
        } else {
            GameFlow.instance = this;
            this.gameReady = true;
        }
    }

    protected start(): void {
        if (!this.gameReady) {
            this.waitSettingUp(window['adsChannels']);
            this.gameReady = true;
        }
        else {
            this.gameManager.setGameState(GameState.Loading);
            
        }
    }

    waitSettingUp(ad_network) {
        switch (ad_network) {
            case "Unity": {
                console.log('init case Unity');
                mraid ? this.initUnity() : this.init();
                break;
            }
            case "Mintegral": {
                if (GameFlow.isLoaded) {
                    console.log('init case Mintegral lately');
                    this.init();
                }
                break;
            }
            default: {
                console.log('init case Default');
                this.init();

                break;
            }
        }
    }

    initUnity() {
        if (!!mraid) {
            this.init();
            return;
        }
        mraid.addEventListener('ready', () => {
            if (mraid?.isViewable()) {
                this.init();
            } else {
                mraid?.addEventListener('viewableChange', () => {
                    this.init();
                });
            }
        });
    }

    init() {

        if (this.gameManager) {
            this.gameManager.setGameState(GameState.Loading);
            SoundManager.Instance.playBgm(SoundManager.Instance.BGM);

        }
        else
        {
            const parent = this.node.parent.parent;
            const soundNode = parent.getChildByName('SoundManager');
            if(soundNode){
                const soundManager = soundNode.getComponent(SoundManager);
                soundManager.initialize();
                soundManager.playBgm(soundManager.BGM);
            }
        }
    }
}


window['advChannels'] = "{{__adv_channels_adapter__}}";

window["gameStart"] = function () {
    if (!GameFlow.instance) {
        console.log('init case Mintegral early');
        GameFlow.isLoaded = true;
    } else {
        console.log('init case Mintegral lately');
        GameFlow.instance.init();
    }
};

window["gameClose"] = function () {
    if (GameFlow.instance) {
        SoundManager.Instance.stopBgm();
    }
};



