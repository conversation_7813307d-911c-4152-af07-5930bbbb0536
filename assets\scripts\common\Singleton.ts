import { _decorator, Component, Node, director, find } from 'cc';
const { ccclass } = _decorator;

@ccclass('Singleton')
export class Singleton<T extends Component> extends Component {
    private static _instances: Map<string, Component> = new Map();

    public static Instance<T extends Component>(this: new () => T): T {
        const className = this.name;
        if (!Singleton._instances.has(className)) {
            let node = find(className);
            if (!node) {
                node = new Node(className);
                director.getScene()?.addChild(node);
            }

            let instance = node.getComponent(this);
            if (!instance) {
                instance = node.addComponent(this);
            }
            Singleton._instances.set(className, instance);
        }
        return Singleton._instances.get(className) as T;
    }

    protected onLoad(): void {
        const className = this.constructor.name;
        if (Singleton._instances.has(className)) {
            const existingInstance = Singleton._instances.get(className);
            if (existingInstance !== this) {
                this.node.destroy();
                return;
            }
        } else {
            Singleton._instances.set(className, this);
        }

        if (this.node.parent === null) {
            director.addPersistRootNode(this.node);
        }
    }

    protected onDestroy(): void {
        const className = this.constructor.name;
        if (Singleton._instances.get(className) === this) {
            Singleton._instances.delete(className);
        }
    }
}