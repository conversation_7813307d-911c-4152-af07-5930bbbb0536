{"name": "pa3", "platform": "web-mobile", "buildPath": "project://build", "debug": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "polyfills": {"asyncFunctions": true, "coreJs": false}, "experimentalEraseModules": false, "useBuiltinServer": false, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": false, "packAutoAtlas": true, "startScene": "b7c05de3-2bb8-427e-8467-c6e1fc35f88c", "outputName": "web-mobile", "scenes": [{"url": "db://assets/scenes/pa3.scene", "uuid": "b7c05de3-2bb8-427e-8467-c6e1fc35f88c", "inBundle": false}, {"url": "db://assets/scenes/pa3-gameplay.scene", "uuid": "0eff8cf7-2de2-4c99-a095-8a2f0dc5238e", "inBundle": false}], "web-mobile": [], "bundleConfigs": [], "packages": {"web-mobile": {"orientation": "auto", "embedWebDebugger": false, "cullEngineAsmJsModule": true, "__version__": "1.0.1"}, "cocos-service": {"configID": "0e26b4", "services": [], "__version__": "3.0.7"}}, "__version__": "1.3.5"}