import { _decorator, Component, Enum, Texture2D, Material, SpriteFrame } from 'cc';
import { ColorType } from '../../enums/Enums';
const { ccclass, property } = _decorator;

@ccclass('ColorMapping')
class ColorMapping {

    @property({
        type: Enum(ColorType),
        tooltip: "The shape of the element this configuration applies to."
    })
    colorType: ColorType = ColorType.None;

    @property(Texture2D)
    texture: Texture2D | null = null;


}

@ccclass('MemeMapping')
export class MemeMapping {
    @property({
        type: Enum(ColorType),
        tooltip: "The color this configuration applies to."
    })
    colorType: ColorType = ColorType.None;

    @property(Texture2D)
    texture: Texture2D | null = null;
}

@ccclass('MemeFrameMapping')
export class MemeSpriteMapping {
    @property({
        type: Enum(ColorType),
        tooltip: "The color this configuration applies to."
    })
    colorType: ColorType = ColorType.None;

    @property(SpriteFrame)
    spriteFrame: SpriteFrame | null = null;
}

@ccclass('ColorConfigs')
export class ColorConfigs extends Component {
    @property([ColorMapping])
    colorMappings: ColorMapping[] = [];

    @property([MemeMapping])
    memeMappings: MemeMapping[] = [];

    @property([MemeSpriteMapping])
    memeSpriteMappings: MemeSpriteMapping[] = [];

    @property(Material)
    outlineMaterial: Material | null = null;
    @property(Material)
    originalMaterial: Material | null = null;

    private static _instance: ColorConfigs | null = null;

    public static get Instance(): ColorConfigs {
        if (!this._instance) {
            console.error("ColorConfigs instance is not yet available.");
        }
        return this._instance!;
    }

    protected onLoad(): void {

        ColorConfigs._instance = this;
    }

    public getTextureFromColor(color: ColorType): Texture2D {
        return this.colorMappings.find(mapping => mapping.colorType === color)?.texture;
    }

    public getTextureMemeFromColor(color: ColorType): Texture2D {
        return this.memeMappings.find(mapping => mapping.colorType === color)?.texture;
    }

    public getSpriteMemeFromColor(color: ColorType): SpriteFrame {
        return this.memeSpriteMappings.find(mapping => mapping.colorType === color)?.spriteFrame;
    }

    public getOutlineMaterial(color: ColorType): Material {
        const texture = this.getTextureFromColor(color);
        this.outlineMaterial.setProperty('mainTexture', texture);
        return this.outlineMaterial;
    }

    public getOriginalMaterial(color: ColorType): Material {
        const texture = this.getTextureFromColor(color);
        this.originalMaterial.setProperty('mainTexture', texture);
        return this.originalMaterial;
    }

}


