import { _decorator, CCInteger, Color, Component, Label, Node, Sprite, tween, Vec3 } from 'cc';
import { LevelDataConfig } from '../enums/LevelDataConfig';
import { AutoLayoutGameplay } from '../app/AutoLayoutGameplay';
import { UIButton } from '../ui/UIButton';
import { EndScreen } from '../city/EndScreen';
import { GameManager } from './GameManager';
import { GameState } from '../enums/GameState';
import { Tutorial } from '../app/Tutorial';
import { SoundManager } from '../app/SoundManager';
import { AutoLayoutGameplay3 } from '../app/AutoLayoutGameplay3';

const { ccclass, property } = _decorator;

@ccclass('UICanvas')
export class UICanvas extends Component {

    @property(CCInteger) private timer: number = 30;
    @property(Node) private containTimer: Node;
    @property(Label) private timerLabel: Label;
    @property(Node) private timeUPFx: Node;
    @property(UIButton) private btnCTA: UIButton;
    @property(UIButton) private btnCTA_Bottom: UIButton;
    @property(EndScreen) private endScreenUI: EndScreen;
    @property(Node) private bottomUI: Node;
    @property(Tutorial) private tutorial: Tutorial = null;

    private _timeRemaining: number = 0;
    private _isGameRunning: boolean = false;
    private _isTimeUp: boolean = false;
    private _levelDataConfig: LevelDataConfig = null;

    private readonly WARNING_TIME: number = 5;
    private readonly NORMAL_COLOR = new Color(255, 255, 255, 255);
    private readonly WARNING_COLOR = new Color(255, 0, 0, 255);

    protected update(dt: number): void {
        if (!this._isGameRunning) return;

        this._timeRemaining -= dt;

        if (this._timeRemaining <= 0) {
            this.handleTimeUp();
            return;
        }

        if (this._timeRemaining <= this.WARNING_TIME) {
            this.playSoundTimeUp();
        }

        this.updateTimerLabel();
    }

    private handleTimeUp(): void {
        this._timeRemaining = 0;
        this._isGameRunning = false;
        GameManager.Instance.setGameState(GameState.Lose);
    }

    private onChangeState(state: GameState): void {
        switch (state) {
            case GameState.Tutorial:
                this.startTutorial();
                break;
            case GameState.Playing:
                this.startGame();
                break;
            case GameState.Win:
                this.showEndScreenComplete();
                break;
            case GameState.Lose:
                this.showEndScreenFail();
                break;
        }
    }

    private startTutorial(): void {
        this.tutorial.node.active = true;
        this.tutorial.startTutorial(this._levelDataConfig.isHorizontal, this._levelDataConfig.isLong);
    }

    private startGame(): void {
        this._isGameRunning = true;
        this.tutorial.onTutorialFinished();
    }

    public setupView(levelDataConfig: LevelDataConfig): void {
        this._levelDataConfig = levelDataConfig;
        this.timer = levelDataConfig.Duration;
        this._timeRemaining = this.timer;

        this.setupAutoLayout(levelDataConfig);
        this.setupEventListeners();
        this.updateTimerLabel();

        this.tutorial.node.active = false;
    }

    private setupAutoLayout(levelDataConfig: LevelDataConfig): void {
        const autoLayout = this.node.getComponent(AutoLayoutGameplay) ||
                          this.node.getComponent(AutoLayoutGameplay3);

        autoLayout?.setTutorialPosition(
            levelDataConfig.tut_pos_landscape,
            levelDataConfig.tut_pos_portrait
        );
    }

    private setupEventListeners(): void {
        GameManager.Instance.onChangeState.on(this.onChangeState, this);

        const ctaHandler = () => this.endScreenUI.callCTA();
        this.btnCTA.InteractedEvent.on(ctaHandler, this);
        this.btnCTA_Bottom.InteractedEvent.on(ctaHandler, this);
    }

    private updateTimerLabel(): void {
        const roundedTime = Math.ceil(this._timeRemaining);
        const timeString = this.formatTime(roundedTime);

        this.timerLabel.string = timeString;
        this.timerLabel.color = this._timeRemaining <= this.WARNING_TIME ?
            this.WARNING_COLOR : this.NORMAL_COLOR;
    }

    private formatTime(totalSeconds: number): string {
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;

        const minutesStr = minutes < 10 ? `0${minutes}` : minutes.toString();
        const secondsStr = seconds < 10 ? `0${seconds}` : seconds.toString();

        return `${minutesStr}:${secondsStr}`;
    }


    private showEndScreenComplete(): void {
        this.endGame(true);
        SoundManager.Instance.playSfx(SoundManager.Instance.Win);
    }

    private showEndScreenFail(): void {
        this.endGame();
        SoundManager.Instance.playSfx(SoundManager.Instance.Game_Over);
    }

    private endGame(isShowParticle: boolean = false): void {
        this._isGameRunning = false;

        this.endScreenUI.node.active = true;
        this.endScreenUI.show(isShowParticle);

        this.hideGameUI();
    }

    private hideGameUI(): void {
        this.timerLabel.node.active = false;
        this.containTimer.active = false;
        this.timeUPFx.active = false;
        this.bottomUI.active = false;
    }

    private playSoundTimeUp(): void {
        if (this._isTimeUp) return;

        this._isTimeUp = true;
        SoundManager.Instance.playSfx(SoundManager.Instance.Time_Out);
        this.showTimeUpFx();
    }

    private showTimeUpFx(): void {
        if (!this.timeUPFx) return;

        const sprite = this.timeUPFx.getComponent(Sprite);
        if (!sprite) {
            this.showSimpleTimeUpFx();
            return;
        }

        this.showAnimatedTimeUpFx();
    }

    private showSimpleTimeUpFx(): void {
        this.timeUPFx.active = true;
        tween(this.timeUPFx)
            .delay(5)
            .call(() => this.timeUPFx && (this.timeUPFx.active = false))
            .start();
    }

    private showAnimatedTimeUpFx(): void {
        this.timeUPFx.active = true;
        tween(this.timeUPFx).stop();
        this.timeUPFx.setScale(Vec3.ONE);

        tween(this.timeUPFx)
            .repeat(10,
                tween()
                    .to(0.25, { scale: new Vec3(1.1, 1.1, 1) })
                    .to(0.25, { scale: Vec3.ONE })
            )
            .call(() => this.timeUPFx && (this.timeUPFx.active = false))
            .start();
    }

}


