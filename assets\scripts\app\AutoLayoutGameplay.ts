import { _decorator, Component, Node, view, Vec3, Camera, Widget } from 'cc';
import { ProgressUnlockCity } from '../city/ProgressUnlockCity';
const { ccclass, property } = _decorator;

@ccclass('AutoLayoutGameplay')
export class AutoLayoutGameplay extends Component implements IAutoLayoutGameplay {
    @property(Node)
    bg: Node = null!;
    @property(Camera)
    camera: Camera = null!;

    @property(Node)
    containLabel: Node = null!;

    @property(ProgressUnlockCity)
    progressUnlockCity: ProgressUnlockCity = null!;

    @property(Node)
    tutorial: Node = null!;

    @property(Node)
    bottomUI: Node = null!;

    @property(Node)
    btn_CTA: Node = null!;

    private cam_pos_landscape: Vec3 = new Vec3(-8, 38, -1.2);
    private cam_pos_portrait: Vec3 = new Vec3(0, 38, -8);

    private cam_rotate: Vec3 = new Vec3(-84, -180, 0);

    private label_scale_landscape: Vec3 = new Vec3(1.2, 1.2, 1.2);
    private label_scale_portrait: Vec3 = new Vec3(1, 1, 1);

    private cam_fov_landscape: number = 40;
    private cam_fov_portrait: number = 60;

    private tut_pos_landscape: Vec3 = new Vec3(-312, -278, 0);
    private tut_pos_portrait: Vec3 = new Vec3(0, 0, 0);

    private tut_scale_landscape: Vec3 = new Vec3(1.6, 1.6, 1.6);
    private tut_scale_portrait: Vec3 = new Vec3(1, 1, 1);

    private bottomUI_scale_landscape: Vec3 = new Vec3(1, 1, 1);
    private bottomUI_scale_portrait: Vec3 = new Vec3(0.6, 0.6, 0.6);


    onLoad() {
        this.updateLayout();
        view.setResizeCallback(this.updateLayout.bind(this));
    }

    onDestroy() {
        view.setResizeCallback(null);
    }

    private updateLayout() {
        const frameSize = view.getFrameSize();
        const isLandscape = frameSize.width > frameSize.height;
        if (!this.bg || !this.camera) return;

        this.bg.children[0].active = !isLandscape;
        this.bg.children[1].active = isLandscape;

        this.camera.node.setPosition(isLandscape ? this.cam_pos_landscape : this.cam_pos_portrait);
        this.camera.node.setRotationFromEuler(this.cam_rotate);
        this.camera.fov = isLandscape ? this.cam_fov_landscape : this.cam_fov_portrait;

        if (this.progressUnlockCity) {
            if (isLandscape) this.progressUnlockCity.landscapeLayout();
            else this.progressUnlockCity.portraitLayout();
        }

        this.containLabel.scale = isLandscape ? this.label_scale_landscape : this.label_scale_portrait;
        this.tutorial.setPosition(isLandscape ? this.tut_pos_landscape : this.tut_pos_portrait);
        this.tutorial.scale = isLandscape ? this.tut_scale_landscape : this.tut_scale_portrait;
        this.bottomUI.scale = isLandscape ? this.bottomUI_scale_landscape : this.bottomUI_scale_portrait;
        this.btn_CTA.getComponent(Widget).bottom = 36;
        this.btn_CTA.getComponent(Widget).right = 48;

        if (this.progressUnlockCity && this.progressUnlockCity.GenerateMask) {
            this.progressUnlockCity.GenerateMask.fx_money.setScale(isLandscape ? this.tut_scale_landscape : this.tut_scale_portrait);
        }
    }

    public setTutorialPosition(tut_pos_landscape: Vec3, tut_pos_portrait: Vec3) {
        this.tut_pos_landscape = tut_pos_landscape;
        this.tut_pos_portrait = tut_pos_portrait;
        this.updateLayout();
    }
}


export interface IAutoLayoutGameplay {
    setTutorialPosition(tut_pos_landscape: Vec3, tut_pos_portrait: Vec3): void;
}


