import { _decorator, CCInteger, Component, Node } from 'cc';
import { BoardController } from '../app/BoardController';
import { LevelManager } from '../app/LevelManager';
import { UICanvas } from './UICanvas';
import { GameData } from '../city/GameData';
import { ProgressUnlockCity } from '../city/ProgressUnlockCity';
const { ccclass, property } = _decorator;

@ccclass('Gameplay')
export class Gameplay extends Component {

    @property(BoardController)
    private boardController: BoardController = null;

    @property(LevelManager)
    private levelManager: LevelManager = null;

    @property(Node)
    private unlockCity: Node = null;

    @property(UICanvas)
    private uiCanvas: UICanvas = null;
   
    private currentLevel: number = 0;

    public initialize() {
        
        this.levelManager.initialize();

        this.currentLevel = GameData.instance.getSelectedCityID();
        const levelDataConfig = this.levelManager.loadLevel(this.currentLevel);
        this.boardController.initializeBoard(levelDataConfig);
        this.unlockCity.getComponent(ProgressUnlockCity).initialize(this.currentLevel);
        // this.uiCanvas.setupView(levelDataConfig);

        
    }

    public hideBoard() {
        this.boardController.node.active = false;
    }
}


