import { _decorator, <PERSON><PERSON><PERSON><PERSON>, Component, director, CCBoolean } from 'cc';
import { UIButton } from '../UIButton';
import { UnlockCity } from '../../city/UnlockCity';
import { GameData } from '../../city/GameData';
const { ccclass, property } = _decorator;

@ccclass('ButtonChoosen')
export class ButtonChoosen extends Component {

    @property(CCInteger) private cityID: number = 0;
    @property(UIButton) private btnChoose: UIButton = null;
    @property(UnlockCity) public unlockCity: UnlockCity = null;
    @property(CCBoolean) public opt2: boolean = false;

    @property(CCBoolean) public isOneClick: boolean = false;

    public onButtonClick: () => void = null;

    start() {
        this.btnChoose.InteractedEvent.on(this.handleButtonClick, this);
    }

    private handleButtonClick() {
        this.onButtonClick?.();
        if (this.isOneClick) return;

        GameData.instance.setSelectedCityID(this.cityID);
        director.loadScene(this.getSceneName());
    }

    private getSceneName(): string {
        const baseScene = this.cityID <= 2 ? 'pa2-gameplay' : 'pa3-gameplay';
        return this.opt2 ? `${baseScene}-opt2` : baseScene;
    }
}


