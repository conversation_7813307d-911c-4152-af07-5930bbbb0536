import { _decorator, AnimationComponent, Component, Node, UIOpacity } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('DirectionHand')
export class DirectionHand extends Component {

    @property(AnimationComponent) public animation: AnimationComponent = null;
    private uiOpacity: UIOpacity = null;

    public isTutorialFinished: boolean = false;

    protected onLoad(): void {
        this.uiOpacity = this.animation.node.getComponent(UIOpacity) || this.animation.node.addComponent(UIOpacity);
    }

    public startTutorial(): void {
        this.animation.node.active = true;
    }
}


