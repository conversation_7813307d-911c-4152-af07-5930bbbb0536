[{"__type__": "cc.SceneAsset", "_name": "pa2", "_objFlags": 0, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "pa2", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 8}, {"__id__": 10}, {"__id__": 15}, {"__id__": 42}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 188}, "_id": "9afa144c-a7a9-4403-b751-d9b05cc43985"}, {"__type__": "cc.Node", "_name": "Enviroment", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 6}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "02BvN2/MZMxqfau/UlU/t5"}, {"__type__": "cc.Node", "_name": "Main Light", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": -0.8660254037844386, "y": 0, "z": 0, "w": 0.5000000000000001}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 0.9999999999999999, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": -120, "y": 0, "z": 0}, "_id": "c0y6F5f+pAvI805TdmxIjx"}, {"__type__": "cc.DirectionalLight", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useColorTemperature": false, "_colorTemperature": 8500, "_staticSettings": {"__id__": 5}, "_visibility": -325058561, "_illuminanceHDR": 65000, "_illuminance": 65000, "_illuminanceLDR": 4, "_shadowEnabled": false, "_shadowPcf": 0, "_shadowBias": 1e-05, "_shadowNormalBias": 0, "_shadowSaturation": 1, "_shadowDistance": 50, "_shadowInvisibleOcclusionRange": 200, "_csmLevel": 4, "_csmLayerLambda": 0.75, "_csmOptimizationMode": 2, "_csmAdvancedOptions": false, "_csmLayersTransition": false, "_csmTransitionRange": 0.05, "_shadowFixedArea": false, "_shadowNear": 0.1, "_shadowFar": 10, "_shadowOrthoSize": 5, "_id": "597uMYCbhEtJQc0ffJlcgA"}, {"__type__": "cc.StaticLightSettings", "_baked": false, "_editorOnly": false, "_castShadow": false}, {"__type__": "cc.Node", "_name": "Main Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 7}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 38, "z": -12}, "_lrot": {"__type__": "cc.Quat", "x": 4.097243276444517e-17, "y": 0.7431448254773941, "z": 0.6691306063588583, "w": -4.5504496591190474e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9999999999999999, "y": 0.9999999999999999, "z": 0.9999999999999999}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": -84.00000000000003, "y": -180, "z": -1.4124500153760508e-30}, "_id": "c9DMICJLFO5IeO07EPon7U"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 6}, "_enabled": true, "__prefab": null, "_projection": 1, "_priority": 0, "_fov": 60, "_fovAxis": 0, "_orthoHeight": 10, "_near": 1, "_far": 1000, "_color": {"__type__": "cc.Color", "r": 51, "g": 51, "b": 51, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 7, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1822425087, "_targetTexture": null, "_cameraType": -1, "_trackingType": 0, "_id": "7dWQTpwS5LrIHnc1zAPUtf"}, {"__type__": "cc.Node", "_name": "SoundManager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 9}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "73xTxpp5ZEs4k5Z2YR2504"}, {"__type__": "b6c059j/LBMRZ+Vt2xEQBus", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": null, "BGM": {"__uuid__": "06d05b0f-8873-4439-842d-9f6acd5549d8", "__expectedType__": "cc.AudioClip"}, "Button_Click": {"__uuid__": "961c172f-47cc-4363-9daa-b84d9054bf39", "__expectedType__": "cc.AudioClip"}, "Clear_Block": {"__uuid__": "c36f9a56-34b5-47a0-887d-e02f73782cd8", "__expectedType__": "cc.AudioClip"}, "Game_Over": {"__uuid__": "43faba26-f489-4ba5-98bc-003c579a87f7", "__expectedType__": "cc.AudioClip"}, "Win": {"__uuid__": "053fb1fb-c6f5-44ee-906c-0ae5c3c7ceac", "__expectedType__": "cc.AudioClip"}, "Level_Complete": {"__uuid__": "7a7bf3de-dd86-433f-b943-b53fd5d23445", "__expectedType__": "cc.AudioClip"}, "Deselect_Block": {"__uuid__": "bbeeb7ac-d14b-4879-979c-baa358028290", "__expectedType__": "cc.AudioClip"}, "Select_Block": {"__uuid__": "a52fa2cd-724b-473b-b8b3-67970c4a8258", "__expectedType__": "cc.AudioClip"}, "Time_Out": {"__uuid__": "2bf34d59-0af8-4c13-b4e5-f68da33de521", "__expectedType__": "cc.AudioClip"}, "Star_Appear": {"__uuid__": "71b6e6a6-c2c6-475e-9404-f03645<PERSON>beed", "__expectedType__": "cc.AudioClip"}, "_id": "bcG03Vn+pIYJxamD2C7lny"}, {"__type__": "cc.Node", "_name": "GameSystem", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 11}, {"__id__": 13}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "cd7QFsQm9Kx6VRn0Lv11q7"}, {"__type__": "cc.Node", "_name": "GameFlow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [], "_active": true, "_components": [{"__id__": 12}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "bf/OaOZzJP14eP2zDtdm3L"}, {"__type__": "67090FWWv9IBaK0NzXmcxUG", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": null, "gameManager": null, "_id": "e1u13cNGlIL7uHHMkGz5CX"}, {"__type__": "cc.Node", "_name": "GameData", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [], "_active": true, "_components": [{"__id__": 14}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d8e7WUny5JPoUeYn52f+q8"}, {"__type__": "1a265gtPM5Ns4UIKBwxfDS1", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": null, "_id": "c0K54PYQNA5IexFK99v410"}, {"__type__": "cc.Node", "_name": "Gameplay", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 16}], "_active": true, "_components": [{"__id__": 23}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b3wmtU8xFOvJuOtalHmTb2"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 15}, "_children": [{"__id__": 17}, {"__id__": 20}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "13dPUbbQlHK4Jt8jWl0ItD"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 16}, "_children": [], "_active": true, "_components": [{"__id__": 18}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -1, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": -3.204653064661855e-18, "y": 0.9986295347545739, "z": -0.05233595624294384, "w": -6.114842316355998e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 4.5, "y": 0.9999999999999999, "z": 6.5}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 6.000000000000002, "y": -180, "z": 8.827812596100317e-32}, "_id": "bfL6IwtXFNjq/s4a2y4I6d"}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "Plane<ModelComponent>", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 17}, "_enabled": true, "__prefab": null, "_materials": [{"__uuid__": "10825948-ef96-4d95-9a66-e594e880423d", "__expectedType__": "cc.Material"}], "_visFlags": 0, "bakeSettings": {"__id__": 19}, "_mesh": {"__uuid__": "1263d74c-8167-4928-91a6-4e2672411f47@2e76e", "__expectedType__": "cc.Mesh"}, "_shadowCastingMode": 0, "_shadowReceivingMode": 1, "_shadowBias": 0, "_shadowNormalBias": 0, "_reflectionProbeId": -1, "_enableMorph": true, "_id": "efP568Xz9MIYimEVt8t55h"}, {"__type__": "cc.ModelBakeSettings", "texture": null, "uvParam": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_bakeable": false, "_castShadow": false, "_receiveShadow": false, "_recieveShadow": false, "_lightmapSize": 64, "_useLightProbe": false, "_bakeToLightProbe": true, "_reflectionProbeType": 0, "_bakeToReflectionProbe": true, "_probeCubemap": null}, {"__type__": "cc.Node", "_name": "bg-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 16}, "_children": [], "_active": false, "_components": [{"__id__": 21}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -6, "y": -1, "z": -4}, "_lrot": {"__type__": "cc.Quat", "x": -3.2046530646618555e-18, "y": 0.9986295347545739, "z": -0.052335956242943835, "w": -6.114842316355998e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 12.000000000000004, "y": 0.9999999999999996, "z": 12.000000000000004}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 6.000000000000001, "y": -180, "z": 0}, "_id": "21Q8cF5WRHpYyYie1h4Y3U"}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "Plane<ModelComponent>", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "_materials": [{"__uuid__": "10825948-ef96-4d95-9a66-e594e880423d", "__expectedType__": "cc.Material"}], "_visFlags": 0, "bakeSettings": {"__id__": 22}, "_mesh": {"__uuid__": "1263d74c-8167-4928-91a6-4e2672411f47@2e76e", "__expectedType__": "cc.Mesh"}, "_shadowCastingMode": 0, "_shadowReceivingMode": 1, "_shadowBias": 0, "_shadowNormalBias": 0, "_reflectionProbeId": -1, "_enableMorph": true, "_id": "fdBDHLNxBPT71x6BQgphuP"}, {"__type__": "cc.ModelBakeSettings", "texture": null, "uvParam": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_bakeable": false, "_castShadow": false, "_receiveShadow": false, "_recieveShadow": false, "_lightmapSize": 64, "_useLightProbe": false, "_bakeToLightProbe": true, "_reflectionProbeType": 0, "_bakeToReflectionProbe": true, "_probeCubemap": null}, {"__type__": "bc7bd7iQglAAplcgWgk39ym", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": null, "boardController": {"__id__": 24}, "levelManager": {"__id__": 41}, "unlockCity": null, "uiCanvas": null, "_id": "09GdayMJZKVqUe29qdXC5O"}, {"__type__": "c9fbcTgZv5Pv43kazZvee+h", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": null, "_enabled": true, "__prefab": null, "floorView": {"__id__": 25}, "obstacleManager": {"__id__": 26}, "gateManager": {"__id__": 31}, "blockManagers": {"__id__": 35}, "_id": "51Kqp4+GNFu6UsVD08/3Zv"}, {"__type__": "7f4fbtqlrNErK1rZP7MyPMg", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": null, "_enabled": true, "__prefab": null, "cellPrefab": {"__uuid__": "28e2702d-c052-4306-be8e-0403194ba88c", "__expectedType__": "cc.Prefab"}, "offsetZ": 0.5, "_id": "fdONqLtrFHVbf26egxqHpT"}, {"__type__": "945e6+P2cdDa5mgNzUUsKtD", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": null, "_enabled": true, "__prefab": null, "obstaclePrefabs": [{"__id__": 27}, {"__id__": 28}, {"__id__": 29}, {"__id__": 30}], "_id": "f0UQ3KltJOBZ3aw7lJyBpZ"}, {"__type__": "ObstaclePrefabItem", "type": 0, "prefab": {"__uuid__": "8ecd179b-40e6-4003-a190-cdaa47c99326", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObstaclePrefabItem", "type": 1, "prefab": {"__uuid__": "9ca8097d-9180-492b-98b8-1cf9c2150f36", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObstaclePrefabItem", "type": 2, "prefab": {"__uuid__": "97abf1e8-ca46-42b7-81d3-226667b9cbfe", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObstaclePrefabItem", "type": 3, "prefab": {"__uuid__": "8a6a2d09-720a-4b5d-bb0d-af68fa557f76", "__expectedType__": "cc.Prefab"}}, {"__type__": "f48f7JNskxCj4xFXsZywuiw", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": null, "_enabled": true, "__prefab": null, "gatePrefabs": [{"__id__": 32}, {"__id__": 33}, {"__id__": 34}], "_id": "cbr5+gS5dK9KKOPoxbi9vy"}, {"__type__": "GatePrefabMapping", "gateType": 1, "prefab": {"__uuid__": "d848b069-2aa6-428f-8af3-2593c32d7f85", "__expectedType__": "cc.Prefab"}}, {"__type__": "GatePrefabMapping", "gateType": 2, "prefab": {"__uuid__": "76b7b698-5492-46b3-962d-f094883d0d57", "__expectedType__": "cc.Prefab"}}, {"__type__": "GatePrefabMapping", "gateType": 3, "prefab": {"__uuid__": "07addf65-b572-4f31-a42d-6ca954952bc0", "__expectedType__": "cc.Prefab"}}, {"__type__": "2ef56yheppPoLUtrzohXwVZ", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": null, "_enabled": true, "__prefab": null, "elementPrefabs": [{"__id__": 36}, {"__id__": 37}, {"__id__": 38}, {"__id__": 39}, {"__id__": 40}], "offsetZ": 1, "_id": "6fRPmOL9pOZZLy/YxLZV4d"}, {"__type__": "ElementPrefabMapping", "shapeName": 1, "prefab": {"__uuid__": "8e789533-0ac5-45f3-aaf1-579ce261bbfc", "__expectedType__": "cc.Prefab"}}, {"__type__": "ElementPrefabMapping", "shapeName": 2, "prefab": {"__uuid__": "7c7ee679-3c2a-4185-8a73-36d438b2142d", "__expectedType__": "cc.Prefab"}}, {"__type__": "ElementPrefabMapping", "shapeName": 3, "prefab": {"__uuid__": "fef66d29-5a07-4f40-9d52-3f3528b7201e", "__expectedType__": "cc.Prefab"}}, {"__type__": "ElementPrefabMapping", "shapeName": 6, "prefab": {"__uuid__": "9f94c442-7504-4669-af50-7fd7a4e276fe", "__expectedType__": "cc.Prefab"}}, {"__type__": "ElementPrefabMapping", "shapeName": 8, "prefab": {"__uuid__": "f5b6bb7f-07c3-432d-b403-b7cb369eb090", "__expectedType__": "cc.Prefab"}}, {"__type__": "d0506x5PIJMPJWOoEd/FVRO", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": null, "_enabled": true, "__prefab": null, "jsonData": {"__uuid__": "db45f6f9-71ab-416a-a3de-4d2c957d60fb", "__expectedType__": "cc.Json<PERSON>set"}, "_id": "0e7CHSPtdOtKQeZf1Qtxui"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 43}, {"__id__": 45}, {"__id__": 154}, {"__id__": 176}], "_active": true, "_components": [{"__id__": 184}, {"__id__": 185}, {"__id__": 186}, {"__id__": 187}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 320, "y": 480, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "67Jrl0gOdCSqgsjljKkXLH"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 42}, "_children": [], "_active": true, "_components": [{"__id__": 44}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e3CakCw/hAAKI6li7Mgznx"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 1073741824, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 480, "_near": 1, "_far": 2000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 6, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 41943040, "_targetTexture": null, "_cameraType": -1, "_trackingType": 0, "_id": "1blg0fpZpKAos+XV3y2v9W"}, {"__type__": "cc.Node", "_name": "UI_choose_city", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 42}, "_children": [{"__id__": 46}, {"__id__": 78}, {"__id__": 104}, {"__id__": 133}], "_active": true, "_components": [{"__id__": 151}, {"__id__": 152}, {"__id__": 153}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "bcBauayshDIqaMl0jCDkzK"}, {"__type__": "cc.Node", "_name": "ButtonCity-Paris", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 45}, "_children": [{"__id__": 47}], "_active": true, "_components": [{"__id__": 74}, {"__id__": 75}, {"__id__": 76}, {"__id__": 77}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -132.153, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "68X2tZSeNLiak/cXcnl8FM"}, {"__type__": "cc.Node", "_name": "City-paris", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 46}, "_children": [{"__id__": 48}, {"__id__": 51}, {"__id__": 54}, {"__id__": 57}, {"__id__": 60}, {"__id__": 63}, {"__id__": 66}, {"__id__": 69}], "_active": true, "_components": [{"__id__": 72}, {"__id__": 73}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 136, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.35, "y": 0.35, "z": 0.35}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "78/s4KbXNEu78eNU8YR9Dx"}, {"__type__": "cc.Node", "_name": "Base", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 47}, "_children": [], "_active": true, "_components": [{"__id__": 49}, {"__id__": 50}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -18.655, "y": -435.715, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f4GqQMCDRG35bkJFiAfl/Z"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 48}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 999, "height": 632}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "5bVOTeY3lKDqyfz//seSth"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 48}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ddb74b44-ee28-416d-83de-6d153ae7fc1d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "8a4V4juhdLX45R4mGLXSU2"}, {"__type__": "cc.Node", "_name": "Sprite-005", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 47}, "_children": [], "_active": true, "_components": [{"__id__": 52}, {"__id__": 53}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 96.123, "y": -28.73, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "daed4EVG5N46YMPPk2fvRe"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 51}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 293, "height": 431}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "18gpi8DzdBdoeV+xXxF/Dj"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 51}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "84dcb9dc-b7dd-4856-ae8d-78cbdd97dbc1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "25nf49/4lApoBBa8uaIGGX"}, {"__type__": "cc.Node", "_name": "Sprite-006", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 47}, "_children": [], "_active": true, "_components": [{"__id__": 55}, {"__id__": 56}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -211.76, "y": -63.32, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7cjWFBXnJN1by36ml9KFY3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 54}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 138, "height": 303}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "73ppWDf0xCP45gypnVHgCB"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 54}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "db7277dd-ed1e-4472-8feb-a9eacd5c6e02@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "76Qe5qTWpIspBFh94oRHfs"}, {"__type__": "cc.Node", "_name": "Sprite-003", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 47}, "_children": [], "_active": true, "_components": [{"__id__": 58}, {"__id__": 59}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -300.102, "y": -349.03, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "73k8MBHvNChangisT9JyHj"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 57}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 245, "height": 184}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "00FhMECrRNBozxn+tp4Uqf"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 57}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "18afd823-fa44-4b54-8aa2-d32e72b934e4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "8eBNCTTWtPOJgVMNvuRJia"}, {"__type__": "cc.Node", "_name": "Sprite-002", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 47}, "_children": [], "_active": true, "_components": [{"__id__": 61}, {"__id__": 62}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -360.737, "y": -139.597, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "dfta0djkpHi73q4UllpYfn"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 219, "height": 285}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "645LzUcz1P843AWC8N+HPy"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "dc55509b-7f2a-4789-a1fe-143d3118e390@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "2dKlyQQN5EBp56nC6yYZEj"}, {"__type__": "cc.Node", "_name": "Sprite-004", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 47}, "_children": [], "_active": true, "_components": [{"__id__": 64}, {"__id__": 65}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 307.795, "y": -249.45, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d8T7EfGqtO5Yb5+uDJZ+3Z"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 63}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 301, "height": 330}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "beO0d6445LC5MXK/h1h/u4"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 63}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "a07b15a7-21b3-4f24-9ad3-935e5df79c6e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "4fIP+YgsRILbqcPOm+rkoh"}, {"__type__": "cc.Node", "_name": "Sprite-007", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 47}, "_children": [], "_active": true, "_components": [{"__id__": 67}, {"__id__": 68}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 327.53, "y": -420.675, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "14XkfnzDtEI5MkDUBnlq9z"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 66}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 125, "height": 162}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "73A8mt0NFIXJOVocGOmsB6"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 66}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8b1c4c1f-aeac-4cb2-a4a1-7f3b2d8b4581@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "f7rQpx9XRE6p83WjfAbXwK"}, {"__type__": "cc.Node", "_name": "Sprite-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 47}, "_children": [], "_active": true, "_components": [{"__id__": 70}, {"__id__": 71}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -100.05, "y": -43.917, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0cyWFISa9EOazB7gCrXOkm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 69}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 322, "height": 570}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "26yqoZs85MRKJujzmrrV08"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 69}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8f34a1dc-76fb-4e0f-a691-042fae2d7da4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "537jjuHstH25G7TYgZtx4w"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 47}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "fdyTyZzFFIGJR3X9l4Fvf2"}, {"__type__": "40b5eZgRRpJQ5L3bs2+GgRG", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 47}, "_enabled": true, "__prefab": null, "isUnlock": true, "_id": "77IGPcb6tPSbfGpmpdb8w7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 46}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 300}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f2+EHAWAlL5bP2k0rt5ZSU"}, {"__type__": "8c2e3Bq3+tHbIhK6acZcV17", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 46}, "_enabled": true, "__prefab": null, "_id": "135Jl/9eFAC5j1sTZrBV9c"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 46}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": "43GCLNdipJyI4P0X0GjVy2"}, {"__type__": "cb2bdjjVahGOIVNWsppRZyR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 46}, "_enabled": true, "__prefab": null, "cityID": 0, "btnChoose": {"__id__": 75}, "unlockCity": {"__id__": 73}, "opt2": false, "_id": "5cJhQFV6JNi4CWgBAkmY/l"}, {"__type__": "cc.Node", "_name": "ButtonCity-Shanghai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 45}, "_children": [{"__id__": 79}], "_active": true, "_components": [{"__id__": 100}, {"__id__": 101}, {"__id__": 102}, {"__id__": 103}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -188.587, "y": 197.875, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "bfRk4yv/hKVZVy0tqxhvFN"}, {"__type__": "cc.Node", "_name": "City-shanghai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 78}, "_children": [{"__id__": 80}, {"__id__": 83}, {"__id__": 86}, {"__id__": 89}, {"__id__": 92}, {"__id__": 95}], "_active": true, "_components": [{"__id__": 98}, {"__id__": 99}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 136, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.28, "y": 0.28, "z": 0.28}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "castU9ZRdFuImqSgDwlnqy"}, {"__type__": "cc.Node", "_name": "Base", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 79}, "_children": [], "_active": true, "_components": [{"__id__": 81}, {"__id__": 82}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -617.164, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e0x1OteZdDcr9C/3eXdzs8"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 80}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 958, "height": 595}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "daVmB5XkZIypi+T+pH4nkn"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 80}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bd943698-3dba-46ab-91a1-746d1dd38008@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "bd9PaXciBGw7+CzbTU/UYT"}, {"__type__": "cc.Node", "_name": "2-5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 79}, "_children": [], "_active": true, "_components": [{"__id__": 84}, {"__id__": 85}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 11.971, "y": -221.461, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7dyk9mOWxKWJMFZhZpn32W"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 83}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 205, "height": 550}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "0eW3Zri2ZJ+5fQNKyO0KPR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 83}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b0293ada-b553-4610-8817-e87e8f35154f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "5dWTZpmuFADL4K4l+KVlxo"}, {"__type__": "cc.Node", "_name": "Sprite-006", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 79}, "_children": [], "_active": true, "_components": [{"__id__": 87}, {"__id__": 88}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 292.165, "y": -310.387, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3bdui7sQBH/IobLuvLeC4G"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 86}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 308, "height": 583}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "10cMNq1DJLkqAE54p7LcC9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 86}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ae55aae8-fbb0-4fe0-9945-bd997199f94e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "50bsCS4dtEP6Aea8EUaKnP"}, {"__type__": "cc.Node", "_name": "Sprite-004", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 79}, "_children": [], "_active": true, "_components": [{"__id__": 90}, {"__id__": 91}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -292.567, "y": -357.065, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "bcQp2bp51Pp7Cll7JpYIdB"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 314, "height": 322}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "08ZNEQmp5LsqKJBCesd1Y0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f61268f6-6ea8-4c4a-b5dd-b3f85dfa8cd2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "11kwN2WmNI2acRbf+owY/r"}, {"__type__": "cc.Node", "_name": "Sprite-003", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 79}, "_children": [], "_active": true, "_components": [{"__id__": 93}, {"__id__": 94}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -165.911, "y": -231.664, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "42e/EtVYFNMKW28T8jAcIM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 92}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 247, "height": 688}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "fcDBvR9TVNVrrSmw4DTQP4"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 92}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "a4f1df9c-3172-4e9f-bae7-341c75748d0d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "adXWk5mtpOgZtzbMUIToEK"}, {"__type__": "cc.Node", "_name": "Sprite-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 79}, "_children": [], "_active": true, "_components": [{"__id__": 96}, {"__id__": 97}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 205.08, "y": -514.547, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "49TOvB/5xIi5nsq48rGetq"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 202, "height": 242}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "cdTo9Vp59MSLyl8+LRNhOW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fcb8ec70-e809-4bbe-aea0-df71ca40b1a9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "3a8In8u8RMJpaVkSoiBFoL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 79}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "bfIqLnbf5DRKfspLGvtJNA"}, {"__type__": "40b5eZgRRpJQ5L3bs2+GgRG", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 79}, "_enabled": true, "__prefab": null, "isUnlock": false, "_id": "faLhvMVIdC9I5jp3w66fvX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 78}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 300}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e5Jw89zHhHCL8SY00OPTQV"}, {"__type__": "8c2e3Bq3+tHbIhK6acZcV17", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 78}, "_enabled": true, "__prefab": null, "_id": "19JWK/ZwVAf5Vhxv51GchB"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 78}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": "98sZxZRphL7KXWJYp4dHVL"}, {"__type__": "cb2bdjjVahGOIVNWsppRZyR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 78}, "_enabled": true, "__prefab": null, "cityID": 1, "btnChoose": {"__id__": 101}, "unlockCity": {"__id__": 99}, "opt2": false, "_id": "4e2h2T5R9J+pcG7c10YyXY"}, {"__type__": "cc.Node", "_name": "ButtonCity-<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 45}, "_children": [{"__id__": 105}], "_active": true, "_components": [{"__id__": 129}, {"__id__": 130}, {"__id__": 131}, {"__id__": 132}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 188.587, "y": 191.445, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e63aCO8u9Hmpm7rMGoWsUJ"}, {"__type__": "cc.Node", "_name": "City-hanoi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 104}, "_children": [{"__id__": 106}, {"__id__": 109}, {"__id__": 112}, {"__id__": 115}, {"__id__": 118}, {"__id__": 121}, {"__id__": 124}], "_active": true, "_components": [{"__id__": 127}, {"__id__": 128}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 136, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.28, "y": 0.28, "z": 0.28}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "6f3dAH8xZPdoLQuCyvJgj0"}, {"__type__": "cc.Node", "_name": "Base", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 105}, "_children": [], "_active": true, "_components": [{"__id__": 107}, {"__id__": 108}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -617.164, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0duTapRTZNG4iKMSL4xuzI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 106}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 960, "height": 818}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ed0wFbeyBBsZjdXWfzDHQ1"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 106}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0d52aac7-9b0b-4e61-ac9b-5ba84f465e35@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "5fav28ITNEwbFr5E8shvps"}, {"__type__": "cc.Node", "_name": "Sprite-006", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 105}, "_children": [], "_active": true, "_components": [{"__id__": 110}, {"__id__": 111}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 215.305, "y": -317.362, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8a6L8Z/+1CSo5rcNQR7zrz"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 293, "height": 354}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "67Z8D2K9tOR7/rYbasrGiY"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "54d34845-d1d4-4be7-b02c-84f74064e2fb@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "8eUrHDDBpNHrg3VzBWK05x"}, {"__type__": "cc.Node", "_name": "Sprite-005", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 105}, "_children": [], "_active": true, "_components": [{"__id__": 113}, {"__id__": 114}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 22.688, "y": -530.687, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "fbR3CbO1RC9L7W2wL3yErS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 112}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 222, "height": 126}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b6CS0CL35CnJcP63DA+dXf"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 112}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f86cbe01-3958-4ea2-aeec-ff5a24861990@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "f59vp3HRJAW5iktoZWi+ZP"}, {"__type__": "cc.Node", "_name": "4-7", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 105}, "_children": [], "_active": true, "_components": [{"__id__": 116}, {"__id__": 117}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -245.9, "y": -306.837, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "fe3RinHuZHDLkvrfUfomJ/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 115}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 468, "height": 445}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4dzVsKe8xGsIkvgrwpa85r"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 115}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1daf4fef-42c8-4efa-b9c7-3ddc2f891b1a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "8eSMiY3XJKiIZb0Rs2RlDJ"}, {"__type__": "cc.Node", "_name": "Sprite-003", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 105}, "_children": [], "_active": true, "_components": [{"__id__": 119}, {"__id__": 120}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 311.85, "y": -546.482, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ff8A9bq8xJSbdZ0q2iawnw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 302, "height": 403}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1dq7Zd+bdKLLfoaa1sNHFM"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b1b6966e-659a-4df3-958e-4f004a6ea22d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "a9vs12a9lMY4UL4cc8BvM9"}, {"__type__": "cc.Node", "_name": "Sprite-002", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 105}, "_children": [], "_active": true, "_components": [{"__id__": 122}, {"__id__": 123}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -90.347, "y": -680.015, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e6+5TmxIJAKYoCf5ExRaQm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 121}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 229, "height": 224}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "102jryBI5IsZv+0N2RCNkI"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 121}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "86c9a895-d84f-4227-8b38-93977c3a05b3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "66gLMYZYtJZLDqP+H4rHnz"}, {"__type__": "cc.Node", "_name": "Sprite-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 105}, "_children": [], "_active": true, "_components": [{"__id__": 125}, {"__id__": 126}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -285.187, "y": -540.295, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8ct4lIBp5MubbQTH73HsZw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 246, "height": 264}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "98R6ExG1FNKLecviLGEUw/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d969c942-32a5-4537-ae59-11141086e3a4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "77ClnewtVIoZjKewQaMvdD"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 105}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "55l5t165JJ26TDD6FI+hkr"}, {"__type__": "40b5eZgRRpJQ5L3bs2+GgRG", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 105}, "_enabled": true, "__prefab": null, "isUnlock": false, "_id": "21DyxPJ1NELZrGCwWjTjyU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 104}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 300}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "5aKx+JPjJB7LHH8O42DRZa"}, {"__type__": "8c2e3Bq3+tHbIhK6acZcV17", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 104}, "_enabled": true, "__prefab": null, "_id": "56Q4fxVlhJk4PmOpW01FJf"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 104}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": "55IQv8MOpGorOzGMUtyUhq"}, {"__type__": "cb2bdjjVahGOIVNWsppRZyR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 104}, "_enabled": true, "__prefab": null, "cityID": 2, "btnChoose": {"__id__": 130}, "unlockCity": {"__id__": 128}, "opt2": false, "_id": "e5ZdNqBbxA3ZbSp/T+7gCd"}, {"__type__": "cc.Node", "_name": "DirectionHand", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 45}, "_children": [{"__id__": 134}], "_active": true, "_components": [{"__id__": 148}, {"__id__": 149}, {"__id__": 150}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -132.15300000000002, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "79C32DXZ1PSKWdhHbCo4ep"}, {"__type__": "cc.Node", "_name": "hand", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 133}, "_children": [{"__id__": 135}, {"__id__": 139}, {"__id__": 143}], "_active": true, "_components": [{"__id__": 146}, {"__id__": 147}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -75.812, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 0.8}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "fasUZ94+ZA07YXq+o+BsRd"}, {"__type__": "cc.Node", "_name": "ring1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 134}, "_children": [], "_active": true, "_components": [{"__id__": 136}, {"__id__": 137}, {"__id__": 138}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "df60e7riRHeom10RaUE1Am"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 135}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 164, "height": 164}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "21tqYwKKpBX7PVkjE/u7s3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 135}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6c3b39d4-d574-436a-8ae5-c6d955925a92@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "08zE/xLERNg5wiw6ycCi+3"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 135}, "_enabled": true, "__prefab": null, "_opacity": 0, "_id": "4ffJHBcKBGGLtmc7JsxaLf"}, {"__type__": "cc.Node", "_name": "ring2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 134}, "_children": [], "_active": true, "_components": [{"__id__": 140}, {"__id__": 141}, {"__id__": 142}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e3KzF9Bx5HrLT8bgLZSbA1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 139}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 164, "height": 164}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "21408qNWFDgKp1jHX0lQBa"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 139}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6c3b39d4-d574-436a-8ae5-c6d955925a92@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "b6ny7kTe5DOZbqPvZ695VO"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 139}, "_enabled": true, "__prefab": null, "_opacity": 0, "_id": "cex4dVvlpBvZ+Pwil4UzhE"}, {"__type__": "cc.Node", "_name": "Hand", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 134}, "_children": [], "_active": true, "_components": [{"__id__": 144}, {"__id__": 145}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.21643961393810288, "w": 0.9762960071199334}, "_lscale": {"__type__": "cc.Vec3", "x": 0.45, "y": 0.45, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 25}, "_id": "c7CYMPK6RNfb74wXOJ+7TT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 143}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 323, "height": 477}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.25, "y": 0.85}, "_id": "70dujTqxNEC4DmaoKJl9qS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 143}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fa7bd01b-4f00-43d4-9fdb-0d3edfab4377@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "07Yuho/kVLlarJmElRqNas"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 134}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "0ck+bxmIpAhZTDnBxqaIDt"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 134}, "_enabled": true, "__prefab": null, "playOnLoad": false, "_clips": [{"__uuid__": "0ddbd80e-5b13-41ac-b286-d7721e35cf57", "__expectedType__": "cc.AnimationClip"}], "_defaultClip": {"__uuid__": "0ddbd80e-5b13-41ac-b286-d7721e35cf57", "__expectedType__": "cc.AnimationClip"}, "_id": "8730gaG8FL24lA/Io2/Av7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 133}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 300}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1avLeuRK5Bw5g6HuhO08HQ"}, {"__type__": "f8b80A+Di5JappnSYJaLpbZ", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 133}, "_enabled": true, "__prefab": null, "animation": {"__id__": 147}, "_id": "61y9/fWV1HxZo5rWTORKnF"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 133}, "_enabled": true, "__prefab": null, "_opacity": 255, "_id": "6cemxtJopO0biad2UPKMMZ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 45}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 960}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b85tu+xQtOsb05eafbmJf3"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 45}, "_enabled": false, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "61SIez5INOqZfiAywCSn3d"}, {"__type__": "c55aeO2z4ZNl5MneqOIe77Z", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 45}, "_enabled": true, "__prefab": null, "tutorialNode": {"__id__": 133}, "_id": "4bCJX6lhpNxZRdZiS8Qx9a"}, {"__type__": "cc.Node", "_name": "BottomUI", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 42}, "_children": [{"__id__": 155}], "_active": true, "_components": [{"__id__": 174}, {"__id__": 175}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e6fvGTAvNOKYzZOsjYr7pR"}, {"__type__": "cc.Node", "_name": "ContainCTA", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 154}, "_children": [{"__id__": 156}, {"__id__": 160}, {"__id__": 167}], "_active": true, "_components": [{"__id__": 171}, {"__id__": 172}, {"__id__": 173}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -424, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "6eZPFAkoNJC5Zns8ukTiLq"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 155}, "_children": [], "_active": true, "_components": [{"__id__": 157}, {"__id__": 158}, {"__id__": 159}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8ckFE7CUlIFK7e31/HXPtK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 156}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 936, "height": 224}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b6N5v54DxNnpdjaYkxbI6x"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 156}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 200}, "_spriteFrame": {"__uuid__": "4cfa9930-c531-418d-9558-5e55ba3ebaf2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "23oZ5OmG1Gvp8VxOQqY14k"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 156}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 114, "_alignMode": 2, "_lockFlags": 45, "_id": "d2XejBJMVNposJCBGI9Z5I"}, {"__type__": "cc.Node", "_name": "Btn_Play", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 155}, "_children": [], "_active": true, "_components": [{"__id__": 161}, {"__id__": 162}, {"__id__": 163}, {"__id__": 164}, {"__id__": 165}, {"__id__": 166}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 165, "y": 23, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1eT63ukjtOZKpcUCJ4ivB+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 160}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 214, "height": 86}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "68Ni4/LT5HXIj+9MLTNz1x"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 160}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fabade8d-544f-4cd7-89cb-0fa3b65145dd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "50vXQG84dIQaQAjHNBZtxj"}, {"__type__": "8c2e3Bq3+tHbIhK6acZcV17", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 160}, "_enabled": true, "__prefab": null, "_id": "4dgEt7riNIbZ2rJIHjmak4"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 160}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "fabade8d-544f-4cd7-89cb-0fa3b65145dd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": "97bkcmGpdEKbC4uQLGv9sb"}, {"__type__": "961aeGllcFJcaHP0vKDnBY9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 160}, "_enabled": true, "__prefab": null, "ctaNode": {"__id__": 160}, "scaleAmount": 1.1, "duration": 0.5, "useBackEasing": false, "_id": "d3JFGToRxFQZ8TxK4P+LtA"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 160}, "_enabled": true, "__prefab": null, "_alignFlags": 36, "_target": {"__id__": 154}, "_left": 0, "_right": 48, "_top": 0, "_bottom": 36, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 36, "_id": "cczA6O5xxGnrFbZFZrsv/L"}, {"__type__": "cc.Node", "_name": "ic_left", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 155}, "_children": [], "_active": true, "_components": [{"__id__": 168}, {"__id__": 169}, {"__id__": 170}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -208, "y": 32, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a3HsIm5cFPy5YvSxaK7CZk"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 167}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 256, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "5bFNyzDUpMprtRUAwtfiHJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 167}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2f7b53c7-0c71-4f42-96f7-e03db210012a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "a30kleBdhKYYNPmilh/HpH"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 167}, "_enabled": true, "__prefab": null, "_alignFlags": 12, "_target": {"__id__": 154}, "_left": 48, "_right": 0, "_top": 0, "_bottom": 24, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 12, "_id": "4c+sVJQptGrbP8TzLEASD9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 155}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 936, "height": 224}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "72nQYHbKFF26WB1YPhBOVJ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 155}, "_enabled": true, "__prefab": null, "_alignFlags": 44, "_target": null, "_left": -148, "_right": -148, "_top": 0, "_bottom": -56, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 44, "_id": "51NBw6QOlMrILd6qm6LvVe"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 155}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "89yznYkRxDwYzQMOwr+zJY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 154}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 960}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "14l1Coc2BHVZdXvqJgNsj9"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 154}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "73milp+CpEgKi+jyF5sDCK"}, {"__type__": "cc.Node", "_name": "spr_tap_to_continue", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 42}, "_children": [{"__id__": 177}], "_active": true, "_components": [{"__id__": 181}, {"__id__": 182}, {"__id__": 183}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 396.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "aa2LdVOF1F36r0Jbj/AHUw"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 176}, "_children": [], "_active": true, "_components": [{"__id__": 178}, {"__id__": 179}, {"__id__": 180}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e0YUFcwaFGCIr66MNSl6PE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 177}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 500, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "3a3jF+6zZDjIHgHtP7wkZW"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 177}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Choose a city", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 42, "_fontSize": 41.9, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "d10be190-c4b3-4ce6-8a85-4fe22bdeb875", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "02Y/7i1ItBEJcO/fdP2lIN"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 177}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_width": 2, "_id": "e0V8OZrw5Ma55xYN2KiFNx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 176}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 745, "height": 95}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c6WZ7v89dIc7WQlYF19oh2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 176}, "_enabled": false, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "01c853a0-36f5-41ed-8315-5bea82b96b4c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "05o9WLXRxNWLqHn0aW9ZEr"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 176}, "_enabled": true, "__prefab": null, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 36, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 1, "_id": "33L9tgmnFKN7GSCHC1iyep"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 42}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 960}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "47xH/V5oBBY5I8PA+S9A6t"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 42}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 44}, "_alignCanvasWithScreen": true, "_id": "4bJ0SKZzVG2bkzIEIjy395"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 42}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "2bHjpJKklAn4cH/bzn8n5a"}, {"__type__": "98811TqK6xA+IbW6pMfejm/", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 42}, "_enabled": true, "__prefab": null, "bg": {"__id__": 16}, "city": {"__id__": 45}, "bottomBtnUI": {"__id__": 154}, "ic_Left": {"__id__": 167}, "btn_CTA": {"__id__": 160}, "_id": "cbiWaXispJ4InlKonJuY2X"}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 189}, "shadows": {"__id__": 190}, "_skybox": {"__id__": 191}, "fog": {"__id__": 192}, "octree": {"__id__": 193}, "lightProbeInfo": {"__id__": 194}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.8627450980392157, "y": 0.9254901960784314, "z": 0.9882352941176471, "w": 0.1}, "_skyIllumLDR": 0.1, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.618555, "y": 0.577848, "z": 0.544564, "w": 0}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 1024, "y": 1024}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": false, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null}]