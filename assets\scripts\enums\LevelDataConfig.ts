import { _decorator, CCBoolean, CCInteger, Enum, Prefab, Vec3 } from 'cc';
import { ColorType, Direction, ElementShape, GateType, ObstacleType } from './Enums';
const { ccclass, property } = _decorator;

@ccclass('ElementPrefabMapping')
export class ElementPrefabMapping {
    @property({
        type: Enum(ElementShape),
        tooltip: "The shape of the element this configuration applies to."
    })
    shapeName: ElementShape = ElementShape.One;
    @property(Prefab)
    prefab: Prefab | null = null;
}

@ccclass('ObstacleDataConfig')
export class ObstacleDataConfig {
    @property({ type: Enum(ObstacleType) })
    public Type: ObstacleType = ObstacleType.Corner;
    @property(Vec3)
    public Position: Vec3 = new Vec3();
    @property(Vec3)
    public Rotation: Vec3 = new Vec3();
}

@ccclass('GateDataConfig')
export class GateDataConfig {
    @property({ type: Enum(GateType) })
    public Type: GateType = GateType.None;
    @property(Vec3)
    public Position: Vec3 = new Vec3();
    @property(Vec3)
    public Rotation: Vec3 = new Vec3();
    @property({ type: Enum(ColorType) })
    public Color: ColorType = ColorType.None;
    @property
    public Size: number = 1;
    @property({ type: Enum(Direction) })
    public Direction: Direction = Direction.Horizontal;
    @property(CCBoolean)
    public HasStar: boolean = false;
}

@ccclass('ElementDataConfig')
export class ElementDataConfig {
    @property({ type: Enum(ElementShape) })
    public Shape: ElementShape = ElementShape.One;
    @property({ type: Enum(ColorType) })
    public Color: ColorType = ColorType.None;
    @property(Vec3)
    public Position: Vec3 = new Vec3();
    @property(Vec3)
    public Rotation: Vec3 = new Vec3();
    @property(CCBoolean)
    public HasStar: boolean = false;
    @property(CCBoolean)
    public HasMeme: boolean = false;
}

@ccclass('LevelDataConfig')
export class LevelDataConfig {

    @property
    public Row: number = 0;

    @property
    public Column: number = 0;
 
    @property([ObstacleDataConfig])
    public Obstacles: ObstacleDataConfig[] = [];

    @property([GateDataConfig])
    public Gates: GateDataConfig[] = [];

    @property([ElementDataConfig])
    public Elements: ElementDataConfig[] = [];

    @property
    private _duration: number = 60;

    @property(Vec3)
    public tut_pos_landscape: Vec3 = new Vec3();
    @property(Vec3)
    public tut_pos_portrait: Vec3 = new Vec3();

    @property(CCBoolean)
    public isHorizontal: boolean = false;

    @property(CCBoolean)
    public isLong: boolean = false;

    constructor(data?: Partial<LevelDataConfig>) {
        if (data) {
            Object.assign(this, data);
           
            this.Obstacles = data.Obstacles?.map(o => Object.assign(new ObstacleDataConfig(), o)) || [];
            this.Gates = data.Gates?.map(g => Object.assign(new GateDataConfig(), g)) || [];
            this.Elements = data.Elements?.map(e => Object.assign(new ElementDataConfig(), e)) || [];
        }
    }

    
    @property
    public get Duration(): number {
        return this._duration;
    }

    
    public set Duration(value: number) {
        this._duration = value;
    }
}

export enum CityElement {
    PARIS = 0,
    SHANGHAI = 1,
    HANOI = 2,
    MARID = 3,
    HONGKONG = 4,
    DUBAI = 5,
}

@ccclass('CityDataConfig')
export class CityDataConfig {
    @property({ type: Enum(CityElement) })
    public Type: CityElement = CityElement.PARIS;
    @property(CCInteger)
    public maxElement: number = 0;
}
