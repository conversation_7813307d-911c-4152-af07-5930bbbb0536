import { _decorator, Component, Enum, Node, Prefab, instantiate } from 'cc';
import { ElementDataConfig, ElementPrefabMapping } from '../enums/LevelDataConfig';
import { BlockController } from '../game/controller/BlockController';
import { BoardController } from './BoardController';
const { ccclass, property } = _decorator;

@ccclass('BlockManager')
export class BlockManager extends Component {

    @property([ElementPrefabMapping])
    private elementPrefabs: ElementPrefabMapping[] = [];

    @property
    offsetZ: number = 0;

    public initialize(blockConfigs: ElementDataConfig[], boardController: BoardController): void {

        for (const blockConfig of blockConfigs) {
            const elementPrefab = this.elementPrefabs.find(element => element.shapeName === blockConfig.Shape)?.prefab;

            if (elementPrefab) {
                const element = instantiate(elementPrefab);
                element.setParent(this.node);

                element.setWorldPosition(blockConfig.Position.x , this.offsetZ, blockConfig.Position.z);
                element.setRotationFromEuler(blockConfig.Rotation);

                const elementController = element.getComponent(BlockController);
                elementController.initialize(blockConfig, boardController);
            }
        }
    }
}


