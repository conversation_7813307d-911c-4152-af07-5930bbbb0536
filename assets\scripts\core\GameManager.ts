import { _decorator, Component, Node, find } from 'cc';
import { GameState } from '../enums/GameState';
import { Signal } from '../eventSystem/Signal';
import { SoundManager } from '../app/SoundManager';
import { Gameplay } from './Gameplay';
import { InputManager } from '../app/InputManager';
const { ccclass, property } = _decorator;

@ccclass('GameManager')
export class GameManager extends Component {

    @property(SoundManager)
    private soundManager: SoundManager = null;

    @property(Gameplay)
    private gameplay: Gameplay = null;

    @property(InputManager)
    private inputManager: InputManager = null;

    private static _instance: GameManager | null = null;

    public static get Instance(): GameManager {
        if (!this._instance) {
            console.error("GameManager instance is not yet available.");
        }
        return this._instance!;
    }

    private _state: GameState = GameState.None;

    public onChangeState: Signal<GameState> = new Signal<GameState>();

    public setGameState(state: GameState) {
        this._state = state;
        this.onChangeState.trigger(this._state);
        this.updateState();
    }

    public getGameState() {
        return this._state;
    }

    private updateState() {
        switch (this._state) {
            case GameState.Loading:
                this.initialize();
                break;

            case GameState.Win:
                this.gameplay.hideBoard();
                break;

            case GameState.Lose:
                this.gameplay.hideBoard();
                break;
        }
    }

    private initialize() {
        GameManager._instance = this;

        this.soundManager.initialize();
        this.gameplay.initialize();
        this.inputManager.initialize();

        this.setGameState(GameState.Tutorial);
    }
}


