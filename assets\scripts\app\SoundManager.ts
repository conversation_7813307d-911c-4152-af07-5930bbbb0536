import { _decorator, AudioClip, AudioSource, Component, No<PERSON>, director } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('SoundManager')
export class SoundManager extends Component {
    private static _instance: SoundManager | null = null;

    public static get Instance(): SoundManager {
        if (!this._instance) {
            console.error("SoundManager instance is not yet available.");
        }
        return this._instance!;
    }


    public initialize() {
        if(SoundManager._instance !== null){
            this.node.destroy();
            return;
        }
        
        SoundManager._instance = this;
        director.addPersistRootNode(this.node);

        this._audioSource = this.node.addComponent(AudioSource);
        this._bgmSource = this.node.addComponent(AudioSource);
        this._bgmSource.loop = true;
        
    }

    @property(AudioClip)
    BGM: AudioClip = null;

    @property(AudioClip)
    Button_Click: AudioClip = null;

    @property(AudioClip)
    Clear_Block: AudioClip = null;

    @property(AudioClip)
    Game_Over: AudioClip = null;

    @property(AudioClip)
    Win: AudioClip = null;

    @property(AudioClip)
    Level_Complete: AudioClip = null;

    @property(AudioClip)
    Deselect_Block: AudioClip = null;

    @property(AudioClip)
    Select_Block: AudioClip = null;

    @property(AudioClip)
    Time_Out: AudioClip = null;

    @property(AudioClip)
    Star_Appear: AudioClip = null;

    private _audioSource: AudioSource = null;
    private _bgmSource: AudioSource = null;


    public playSfx(sfxClips: AudioClip, volume: number = 1.0) {
        this._audioSource.clip = sfxClips;
        this._audioSource.volume = volume;
        this._audioSource.play();
    }

    public playBgm(bgmClips: AudioClip, volume: number = 1.0) {
        this._bgmSource.clip = bgmClips;
        this._bgmSource.volume = .5;
        this._bgmSource.play();
    }

    public stopBgm() {
        this._bgmSource.stop();
    }
    
    public pauseBgm() {
        this._bgmSource.pause();
    }

    
}


