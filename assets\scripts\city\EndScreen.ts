import { _decorator, Component, Node, ParticleSystem2D, sys, tween, Vec3 } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('EndScreen')
export class EndScreen extends Component {

    @property(Node) private logo: Node;
    @property(ParticleSystem2D) private particle: ParticleSystem2D;
    @property(ParticleSystem2D) private particle2: ParticleSystem2D;

    private particleTween: any = null;
    private _isShowParticle: boolean = false;

    public show(isShowParticle: boolean = false) {
        if (this._isShowParticle) return;

        this.node.active = true;
        this._isShowParticle = true;

        this.handleMintegralAd()

        this.setupParticles(isShowParticle);
        // this.animateLogo();
        this.callCTA();
    }

    private handleMintegralAd(): boolean {
        const adNetwork = window['advChannels'];
        if (adNetwork === "Mintegral") {
            window['gameEnd']?.();
            return true;
        }
        return false;
    }

    private setupParticles(isShowParticle: boolean) {
        if (!this.particle || !this.particle2 || !isShowParticle) return;

        this.particleTween?.stop();

        this.particle.resetSystem();
        this.particle2.resetSystem();

        this.particleTween = tween(this.particle)
            .repeatForever(
                tween()
                    .delay(4)
                    .call(() => this.particle.resetSystem())
            )
            .start();
    }

    private animateLogo() {
        const originalPos = this.logo.position.clone();

        this.logo.setPosition(new Vec3(originalPos.x, originalPos.y + 200, originalPos.z));
        this.logo.setScale(Vec3.ZERO);

        tween(this.logo)
            .delay(0.5)
            .parallel(
                tween().to(0.25, { position: originalPos }, { easing: 'quartOut' }),
                tween().to(0.25, { scale: Vec3.ONE })
            )
            .start();

        tween(this.logo)
            .delay(0.5)
            .call(() => this.callCTA())
            .start();
    }

    public callCTA() {
        console.log('callCTA');
        const adNetwork = window['advChannels'];

        if (!adNetwork) return;

        switch (adNetwork) {
            case "Mintegral":
                window['install']?.();
                break;

            case "Unity":
            case "AppLovin":
                this.openAppStore();
                break;
        }
    }

    private openAppStore() {
        const isAndroid = sys.os === sys.OS.ANDROID;
        const storeFunction = isAndroid ?
            window["mraidOpenPlayStore"] :
            window["mraidOpenAppStore"];

        storeFunction?.();
    }
}


