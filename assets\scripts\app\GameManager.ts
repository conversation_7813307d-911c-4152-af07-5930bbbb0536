import { _decorator, CCInteger, Color, Component, Label, Node, Sprite, tween, Vec3 } from 'cc';
import { LevelManager } from './LevelManager';
import { BoardController } from './BoardController';
import { delay } from '../utils/AsyncUtils';
import { SoundManager } from './SoundManager';
import { UIButton } from '../ui/UIButton';
import { EndScreen } from '../city/EndScreen';
import { ProgressUnlockCity } from '../city/ProgressUnlockCity';
import { GameData } from '../city/GameData';
import { Tutorial } from './Tutorial';
const { ccclass, property } = _decorator;


@ccclass('GameManager')
export class GameManager extends Component {

    @property(LevelManager)
    levelManager: LevelManager = null;
    // @property(BoardController)
    boardController: BoardController = null;

    @property(CCInteger)
    currentLevel: number = 0;

    @property(CCInteger) private timer: number = 90;
    @property(Node) private containTimer: Node;
    @property(Label) private timerLabel: Label;
    @property(Node) private endScreen: Node;
    @property(Node) private timeUPFx: Node;
    @property(UIButton) private btnCTA: UIButton;
    @property(EndScreen) private endScreenUI: EndScreen;

    @property(Tutorial) public tutorial: Tutorial;

    private _isGameRunning: boolean = false;
    private _timeRemaining: number = 0;
    private readonly WARNING_TIME: number = 5; 

    private static _instance: GameManager | null = null;

    private _isTimeUp: boolean = false;

    public static get Instance(): GameManager {
        if (!this._instance) {
            console.error("GameManager instance is not yet available.");
        }
        return this._instance!;
    }
    protected onLoad(): void {
        GameManager._instance = this;

        this.endScreen.active = false;
        this.btnCTA.InteractedEvent.on(() => {
            console.log("CTA button clicked");
            this.endScreenUI.callCTA();
        }, this);
    }

    protected start(): void {
        this.initGame();
        this.startTutorial();
    }

    private showTimeUpFx(): void {
        console.log("showTimeUpFx");

        if (!this.timeUPFx) {
            console.warn("timeUPFx node is not assigned in GameManager.");
            return;
        }

        // Check if it's a sprite, for fallback, though scale animation is on the node itself.
        const sprite = this.timeUPFx.getComponent(Sprite);
        if (!sprite) {
            console.warn("timeUPFx node does not have a Sprite component. Performing simple on/off fallback.");
            this.timeUPFx.active = true;
            tween(this.timeUPFx)
                .delay(5)
                .call(() => { if (this.timeUPFx) this.timeUPFx.active = false; })
                .start();
            return;
        }

        this.timeUPFx.active = true;
        tween(this.timeUPFx).stop(); // Stop any previous tweens on this node

        this.timeUPFx.setScale(1, 1, 1); // Ensure initial scale is normal

        tween(this.timeUPFx)
            .repeat(10, // Repeat the following sequence 10 times (0.5s per cycle * 10 = 5s total)
                tween()
                    .to(0.25, { scale: new Vec3(1.1, 1.1, 1) }) // Scale up slightly
                    .to(0.25, { scale: new Vec3(1, 1, 1) })   // Scale back to normal
            )
            .call(() => {
                if (this.timeUPFx) { // Check if node still exists
                    this.timeUPFx.active = false; // Hide after repetitions
                }
            })
            .start();
    }

    private stopTimeUpFx(): void {
        if (this.timeUPFx) {
            this.timeUPFx.active = false;
        }
    }


    public startTutorial(): void {
        this.tutorial.startTutorial();
    }

    private initGame(): void {
        delay(10).then(() => {
            this.currentLevel = GameData.instance.getSelectedCityID();
            const levelDataConfig = this.levelManager.loadLevel(this.currentLevel);
            this.boardController.initializeBoard(levelDataConfig);

            this.timer = levelDataConfig.Duration;

            this._timeRemaining = this.timer;
            
            this.updateTimerLabel();
        });
        
        
    }

    public startGame() {

        this._isGameRunning = true;
    }

    protected update(deltaTime: number): void {
        if (!this._isGameRunning) return;

        
        this._timeRemaining -= deltaTime;

       
        if (this._timeRemaining <= 0) {
            this._timeRemaining = 0;
            this._isGameRunning = false;
            // console.log("Time's up!");

            this.showEndScreenFail();


        }

        if (this._timeRemaining <= 5) {
            this.playSoundTimeUp();
        }

        this.updateTimerLabel();
    }
    public finishTutorial(): void {
        this.tutorial.onTutorialFinished();
        this.startGame();
    }   

    private updateTimerLabel(): void {
        // Làm tròn lên số giây
        const roundedTime = Math.ceil(this._timeRemaining);
        const minutes = Math.floor(roundedTime / 60);
        const seconds = Math.floor(roundedTime % 60);

        const minutesStr = minutes < 10 ? "0" + minutes : minutes.toString();
        const secondsStr = seconds < 10 ? "0" + seconds : seconds.toString();
        this.timerLabel.string = `${minutesStr}:${secondsStr}`;

        if ( this._timeRemaining <= this.WARNING_TIME) {
            this.timerLabel.color = new Color(255, 0, 0, 255); // Màu đỏ
            
        } else {
            this.timerLabel.color = new Color(255, 255, 255, 255); // Màu trắng
            
        }
    }

    private playSoundTimeUp(): void {
        if (this._isTimeUp) return;
        this._isTimeUp = true;
        SoundManager.Instance.playSfx(SoundManager.Instance.Time_Out);
        this.showTimeUpFx();
    }

    public showEndScreenComplete(): void {
        this.endGame(true);
        ProgressUnlockCity.Instance.playAnimationEndGame(true);
        SoundManager.Instance.playSfx(SoundManager.Instance.Win);
    }

    public showEndScreenFail(): void {
        this.endGame();
        ProgressUnlockCity.Instance.playAnimationEndGame(false);
        SoundManager.Instance.playSfx(SoundManager.Instance.Game_Over);
    }

    private endGame(isShowParticle: boolean = false): void {
        this.timeStop();
        this.endScreen.active = true;
        this.endScreenUI.show(isShowParticle);

        this.boardController.node.active = false;
        this.timerLabel.node.active = false;
        this.containTimer.active = false;
        this.stopTimeUpFx();
    }

    public timeStop(): void {
        this._isGameRunning = false;
    }


}