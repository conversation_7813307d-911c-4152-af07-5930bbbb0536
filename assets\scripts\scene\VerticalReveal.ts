import { _decorator, Component, Node, UITransform, tween, Vec3, size } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('VerticalReveal')
export class VerticalReveal extends Component {

    @property(Node)
    public targetSpriteNode: Node = null; // Kéo SpriteContainer vào đây

    private originalHeight: number = 0;
    private uiTransform: UITransform = null;

   

    public setup() {
        this.uiTransform = this.targetSpriteNode.getComponent(UITransform);
        if (this.uiTransform) {
            // Lưu lại chiều cao ban đầu
            this.originalHeight = this.uiTransform.height;
            // Bắt đầu ẩn đi
            this.uiTransform.height = 0;
        }
    }

    playReveal(callback: () => void) {
        if (!this.uiTransform) return;
        // console.log('playReveal');
        tween(this.uiTransform)
            .delay(0.4)
            .to(.9, { height: this.originalHeight }, { easing: 'cubicOut' })
            .call(() => {
                callback();
            })
            .start();
    }
}