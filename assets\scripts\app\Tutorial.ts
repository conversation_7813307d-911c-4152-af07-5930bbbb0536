import { _decorator, Animation, AnimationComponent, Component, Node, tween, Vec3, UIOpacity } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('Tutorial')
export class Tutorial extends Component {

    @property(AnimationComponent) public animation: AnimationComponent = null;
    @property private fadeInDuration: number = 0.3;
    @property private moveDuration: number = 0.4;
    @property private fadeOutDuration: number = 0.3;
    @property private loopDelay: number = 0.5;

    private uiOpacity: UIOpacity = null;
    private nodeHand: Node = null;
    private originalPosition: Vec3 = Vec3.ZERO.clone();
    private isHorizontal: boolean = true;
    private isLong: boolean = false;

    public isTutorialFinished: boolean = false;

    protected onLoad(): void {
        this.setupComponents();
        this.setupEventListeners();
    }

    private setupComponents(): void {
        this.uiOpacity = this.animation.node.getComponent(UIOpacity) ||
                        this.animation.node.addComponent(UIOpacity);
        this.originalPosition = this.animation.node.getPosition().clone();
        this.nodeHand = this.animation.node.getChildByName('Hand');
    }

    private setupEventListeners(): void {
        this.animation.on(Animation.EventType.FINISHED, this.onAnimationFinished, this);
    }

    public startTutorial(isHorizontal: boolean = true, isLong: boolean = false): void {
        this.isHorizontal = isHorizontal;
        this.isLong = isLong;

        this.resetTutorialState();
        this.fadeInAndPlay();
    }

    private resetTutorialState(): void {
        this.animation.node.active = true;
        this.animation.node.setPosition(this.originalPosition);
        this.uiOpacity.opacity = 0;
        this.nodeHand.active = true;
    }

    private fadeInAndPlay(): void {
        tween(this.uiOpacity)
            .to(this.fadeInDuration, { opacity: 255 }, { easing: 'quartOut' })
            .call(() => this.animation.play())
            .start();
    }

    private onAnimationFinished(): void {
        const targetPosition = this.calculateTargetPosition();

        tween(this.animation.node)
            .to(this.moveDuration, { position: targetPosition }, { easing: 'quartInOut' })
            .delay(this.loopDelay)
            .call(() => this.fadeOutAndLoop())
            .start();
    }

    private calculateTargetPosition(): Vec3 {
        const moveDistance = 75;
        const extraDistance = this.isLong ? 40 : 0;
        const totalDistance = moveDistance + extraDistance;

        return this.isHorizontal
            ? new Vec3(this.originalPosition.x + totalDistance, this.originalPosition.y, this.originalPosition.z)
            : new Vec3(this.originalPosition.x, this.originalPosition.y - moveDistance, this.originalPosition.z);
    }

    private fadeOutAndLoop(): void {
        tween(this.uiOpacity)
            .to(this.fadeOutDuration, { opacity: 0 }, { easing: 'quartIn' })
            .delay(this.loopDelay)
            .call(() => this.handleLoopComplete())
            .start();
    }

    private handleLoopComplete(): void {
        if (!this.isTutorialFinished) {
            this.nodeHand.active = false;
            this.startTutorial(this.isHorizontal, this.isLong);
        }
    }

    public onTutorialFinished(): void {
        this.isTutorialFinished = true;
        this.fadeOutAndHide();
    }

    private fadeOutAndHide(): void {
        tween(this.uiOpacity)
            .to(this.fadeOutDuration, { opacity: 0 }, { easing: 'quartIn' })
            .call(() => {
                this.animation.node.active = false;
                this.node.active = false;
            })
            .start();
    }


}


