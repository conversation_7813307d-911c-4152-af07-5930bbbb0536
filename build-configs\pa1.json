{"name": "pa1", "platform": "web-mobile", "buildPath": "project://build", "debug": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "polyfills": {"asyncFunctions": true, "coreJs": false}, "experimentalEraseModules": false, "useBuiltinServer": false, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": false, "packAutoAtlas": true, "startScene": "4c2529d0-c71e-40d9-8e0a-70a5dde20b46", "outputName": "web-mobile", "scenes": [{"url": "db://assets/scenes/pa1.scene", "uuid": "4c2529d0-c71e-40d9-8e0a-70a5dde20b46", "inBundle": false}], "web-mobile": [], "bundleConfigs": [], "packages": {"web-mobile": {"orientation": "auto", "embedWebDebugger": false, "cullEngineAsmJsModule": true, "__version__": "1.0.1"}, "cocos-service": {"configID": "0e26b4", "services": [], "__version__": "3.0.7"}}, "__version__": "1.3.5"}