import { _decorator, Component, Label, Node, Sprite, SpriteFrame } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('MemeGame')
export class MemeGame extends Component {
    @property(Sprite) private icon: Sprite = null;
    @property(Label) private lblAmount: Label = null;

    private _amount: number = 0;

    public setupView(icon: SpriteFrame, amount: number) {
        this.icon.spriteFrame = icon;
        this._amount = amount;
        this.lblAmount.string = this._amount.toString();
    }

    public updateAmount() {
        this._amount--;
        this.lblAmount.string = this._amount.toString();
    }

    public get Amount(): number {
        return this._amount;
    }
}


