import { _decorator, Component, CCInteger } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('GameData')
export class GameData extends Component {

    private static _instance: GameData = null;
    @property(CCInteger)
    private selectedCityID: number = 0;

    public static get instance(): GameData {
        return this._instance;
    }

    protected onLoad(): void {
        if (GameData._instance === null) {
            GameData._instance = this;
        } else {
            this.node.destroy();
        }
    }

    public setSelectedCityID(cityID: number) {
        this.selectedCityID = cityID;
    }

    public getSelectedCityID(): number {
        return this.selectedCityID;
    }

    
} 