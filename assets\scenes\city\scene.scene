[{"__type__": "cc.SceneAsset", "_name": "scene", "_objFlags": 0, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "scene", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 5}, {"__id__": 7}, {"__id__": 13}], "_active": true, "_components": [], "_prefab": {"__id__": 39}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 40}, "_id": "fdf738a9-fc85-4bc3-8e61-9165ac4b75f2"}, {"__type__": "cc.Node", "_name": "Main Light", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": -0.06397656665577071, "y": -0.44608233363525845, "z": -0.8239028751062036, "w": -0.3436591377065261}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": -117.894, "y": -194.909, "z": 38.562}, "_id": "c0y6F5f+pAvI805TdmxIjx"}, {"__type__": "cc.DirectionalLight", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 255, "g": 250, "b": 240, "a": 255}, "_useColorTemperature": false, "_colorTemperature": 6550, "_staticSettings": {"__id__": 4}, "_visibility": -325058561, "_illuminanceHDR": 65000, "_illuminance": 65000, "_illuminanceLDR": 1.6927083333333335, "_shadowEnabled": false, "_shadowPcf": 0, "_shadowBias": 1e-05, "_shadowNormalBias": 0, "_shadowSaturation": 1, "_shadowDistance": 50, "_shadowInvisibleOcclusionRange": 200, "_csmLevel": 4, "_csmLayerLambda": 0.75, "_csmOptimizationMode": 2, "_csmAdvancedOptions": false, "_csmLayersTransition": false, "_csmTransitionRange": 0.05, "_shadowFixedArea": false, "_shadowNear": 0.1, "_shadowFar": 10, "_shadowOrthoSize": 5, "_id": "597uMYCbhEtJQc0ffJlcgA"}, {"__type__": "cc.StaticLightSettings", "_baked": false, "_editorOnly": false, "_castShadow": false}, {"__type__": "cc.Node", "_name": "Main Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 6}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -10, "y": 10, "z": 10}, "_lrot": {"__type__": "cc.Quat", "x": -0.27781593346944056, "y": -0.36497167621709875, "z": -0.11507512748638377, "w": 0.8811195706053617}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": -35, "y": -45, "z": 0}, "_id": "c9DMICJLFO5IeO07EPon7U"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_projection": 1, "_priority": 0, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 10, "_near": 1, "_far": 1000, "_color": {"__type__": "cc.Color", "r": 51, "g": 51, "b": 51, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 14, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1822425087, "_targetTexture": null, "_cameraType": -1, "_trackingType": 0, "_id": "7dWQTpwS5LrIHnc1zAPUtf"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 8}], "_active": true, "_components": [{"__id__": 10}, {"__id__": 11}, {"__id__": 12}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 320, "y": 480, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e33DPz5MxJ/JyrZeFNqjnY"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 9}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "02DocGm45Hr4H3Rlw7h5qi"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 1073741824, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 480, "_near": 1, "_far": 2000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 6, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 41943040, "_targetTexture": null, "_cameraType": -1, "_trackingType": 0, "_id": "1dFbCTQbdF1qkmypaEUzFk"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 7}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 960}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "cc21HvQFNNX63Bri4TR8FG"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 7}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 9}, "_alignCanvasWithScreen": true, "_id": "387yye/7dBi5tLBVMkOMfV"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 7}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "d8UEAUdc1FzIE4p6Mj4CzI"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 14}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 13}, "asset": {"__uuid__": "7c7ee679-3c2a-4185-8a73-36d438b2142d", "__expectedType__": "cc.Prefab"}, "fileId": "0cWaOgGVRCy5+QfYKEz8Oh", "instance": {"__id__": 15}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "860vg4Ia5BUIlonirkZJ25", "prefabRootNode": null, "mountedChildren": [{"__id__": 16}, {"__id__": 21}], "mountedComponents": [{"__id__": 26}], "propertyOverrides": [{"__id__": 30}, {"__id__": 32}, {"__id__": 33}, {"__id__": 34}, {"__id__": 35}, {"__id__": 37}], "removedComponents": []}, {"__type__": "cc.MountedChildrenInfo", "targetInfo": {"__id__": 17}, "nodes": [{"__id__": 18}]}, {"__type__": "cc.TargetInfo", "localID": ["45o5aDQOlHYpNamICEt5wh"]}, {"__type__": "cc.Node", "_name": "C<PERSON>", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 13}}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 19}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0.4, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "48v6ZIb5RDypaRqEhi5VzQ"}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "Cube<ModelComponent>", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "__prefab": null, "_materials": [{"__uuid__": "620b6bf3-0369-4560-837f-2a2c00b73c26", "__expectedType__": "cc.Material"}], "_visFlags": 0, "bakeSettings": {"__id__": 20}, "_mesh": {"__uuid__": "1263d74c-8167-4928-91a6-4e2672411f47@a804a", "__expectedType__": "cc.Mesh"}, "_shadowCastingMode": 0, "_shadowReceivingMode": 1, "_shadowBias": 0, "_shadowNormalBias": 0, "_reflectionProbeId": -1, "_enableMorph": true, "_id": "82ESRZfpJOVoTvGG0Cshmb"}, {"__type__": "cc.ModelBakeSettings", "texture": null, "uvParam": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_bakeable": false, "_castShadow": false, "_receiveShadow": false, "_recieveShadow": false, "_lightmapSize": 64, "_useLightProbe": false, "_bakeToLightProbe": true, "_reflectionProbeType": 0, "_bakeToReflectionProbe": true, "_probeCubemap": null}, {"__type__": "cc.MountedChildrenInfo", "targetInfo": {"__id__": 22}, "nodes": [{"__id__": 23}]}, {"__type__": "cc.TargetInfo", "localID": ["f2HlMXFn5DHpUFxLVuobj1"]}, {"__type__": "cc.Node", "_name": "C<PERSON>", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 13}}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 24}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0.4, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "63ARt+hbJGt5Mu9i5pj1Il"}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "Cube<ModelComponent>", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "__prefab": null, "_materials": [{"__uuid__": "620b6bf3-0369-4560-837f-2a2c00b73c26", "__expectedType__": "cc.Material"}], "_visFlags": 0, "bakeSettings": {"__id__": 25}, "_mesh": {"__uuid__": "1263d74c-8167-4928-91a6-4e2672411f47@a804a", "__expectedType__": "cc.Mesh"}, "_shadowCastingMode": 0, "_shadowReceivingMode": 1, "_shadowBias": 0, "_shadowNormalBias": 0, "_reflectionProbeId": -1, "_enableMorph": true, "_id": "44iofXp6lMN5Ausd6N8VFF"}, {"__type__": "cc.ModelBakeSettings", "texture": null, "uvParam": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_bakeable": false, "_castShadow": false, "_receiveShadow": false, "_recieveShadow": false, "_lightmapSize": 64, "_useLightProbe": false, "_bakeToLightProbe": true, "_reflectionProbeType": 0, "_bakeToReflectionProbe": true, "_probeCubemap": null}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 27}, "components": [{"__id__": 28}]}, {"__type__": "cc.TargetInfo", "localID": ["45o5aDQOlHYpNamICEt5wh"]}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 13}}, "node": null, "_enabled": true, "__prefab": null, "_materials": [], "_visFlags": 0, "bakeSettings": {"__id__": 29}, "_mesh": null, "_shadowCastingMode": 0, "_shadowReceivingMode": 1, "_shadowBias": 0, "_shadowNormalBias": 0, "_reflectionProbeId": -1, "_enableMorph": true, "_id": "6eAHqQisZLiJ20GSLqICyQ"}, {"__type__": "cc.ModelBakeSettings", "texture": null, "uvParam": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_bakeable": false, "_castShadow": false, "_receiveShadow": false, "_recieveShadow": false, "_lightmapSize": 64, "_useLightProbe": false, "_bakeToLightProbe": true, "_reflectionProbeType": 0, "_bakeToReflectionProbe": true, "_probeCubemap": null}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 31}, "propertyPath": ["_name"], "value": "element-2"}, {"__type__": "cc.TargetInfo", "localID": ["0cWaOgGVRCy5+QfYKEz8Oh"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 31}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 31}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0.7071067811865475, "z": 0, "w": 0.7071067811865476}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 31}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 90, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 36}, "propertyPath": ["checkCollision"], "value": {"__id__": 18}}, {"__type__": "cc.TargetInfo", "localID": ["44phtLtRNBOYAvRmYCYW94"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 38}, "propertyPath": ["checkCollision"], "value": {"__id__": 23}}, {"__type__": "cc.TargetInfo", "localID": ["76cbByJNtPj62jIs9plK5j"]}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "fdf738a9-fc85-4bc3-8e61-9165ac4b75f2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 13}]}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 41}, "shadows": {"__id__": 42}, "_skybox": {"__id__": 43}, "fog": {"__id__": 44}, "octree": {"__id__": 45}, "lightProbeInfo": {"__id__": 46}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.452588, "y": 0.607642, "z": 0.755699, "w": 0}, "_skyIllumLDR": 0.8, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.618555, "y": 0.577848, "z": 0.544564, "w": 0}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 1024, "y": 1024}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null}]