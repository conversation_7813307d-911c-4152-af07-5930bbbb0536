import { _decorator, Component, Node } from 'cc';
import { EndScreen } from '../city/EndScreen';
import { UIButton } from '../ui/UIButton';
const { ccclass, property } = _decorator;

@ccclass('OneClickOption')
export class OneClickOption extends Component {
    @property(EndScreen) public endScreen: EndScreen = null!;

    @property(UIButton) private btnCTA: UIButton = null!;
    @property(Node) private nodeToHide: Node[] = [];

    protected onLoad(): void {
        this.endScreen.node.active = false;
    }

    start() {


        this.btnCTA.InteractedEvent.on(() => {
            this.endScreen.callCTA();
        }, this);
    }

    public actionOneClick() {
        this.nodeToHide.forEach(node => node.active = false);
        this.endScreen.show(true);
    }

}


