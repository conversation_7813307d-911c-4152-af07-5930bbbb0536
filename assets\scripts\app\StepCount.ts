import { _decorator, Component, Node, CCInteger } from 'cc';
import { GameManager } from '../core/GameManager';
import { GameState } from '../enums/GameState';
const { ccclass, property } = _decorator;

@ccclass('StepCount')
export class StepCount extends Component {
    private static _instance: StepCount | null = null;
    @property(CCInteger) 
    public maxStepCount: number = 8;
    private _stepCount: number = 0;

    public static get Instance(): StepCount {
        if (!this._instance) {
            // console.error("StepCount instance is not yet available.");  
        }
        return this._instance!;
    }

    protected onLoad(): void {
        StepCount._instance = this;
    }

    public addStepCount() {
        this._stepCount++;

        if(this._stepCount >= this.maxStepCount){
            GameManager.Instance.setGameState(GameState.Win);
            return;
        }
    }
}


