import { _decorator, Component, Vec3, PhysicsSystem, Quat, geometry } from 'cc';
import { GateController } from './GateController';

const { ccclass } = _decorator;

@ccclass('ColliderUnit')
export class ColliderUnit extends Component {

    public checkColliderGate(): GateController[] | null {
        const cellSize = 2;
        const halfSize = cellSize / 2;
        const targetPos = this.node.worldPosition.clone();
        const rotate = this.node.parent.parent.eulerAngles.y;
        const rotationQuat = new Quat();
        Quat.fromEuler(rotationQuat, 0, rotate, 0);

        const worldPoints: Vec3[] = [];

        const farLeft = new Vec3();
        Vec3.transformQuat(farLeft, new Vec3(-halfSize, 0, halfSize), rotationQuat);
        Vec3.add(farLeft, targetPos, farLeft);
        worldPoints.push(farLeft);

        const farRight = new Vec3();
        Vec3.transformQuat(farRight, new Vec3(halfSize, 0, halfSize), rotationQuat);
        Vec3.add(farRight, targetPos, farRight);
        worldPoints.push(farRight);

        const nearRight = new Vec3();
        Vec3.transformQuat(nearRight, new Vec3(halfSize, 0, -halfSize), rotationQuat);
        Vec3.add(nearRight, targetPos, nearRight);
        worldPoints.push(nearRight);

        const nearLeft = new Vec3();
        Vec3.transformQuat(nearLeft, new Vec3(-halfSize, 0, -halfSize), rotationQuat);
        Vec3.add(nearLeft, targetPos, nearLeft);
        worldPoints.push(nearLeft);

        const mask = PhysicsSystem.PhysicsGroup.DEFAULT;
        const queryTrigger = true;
        const gates: GateController[] = [];
        const foundGates = new Set<GateController>();

        const rayDirections = [
            new Vec3(-halfSize, 0, -halfSize),
            new Vec3(halfSize, 0, -halfSize),
            new Vec3(halfSize, 0, halfSize),
            new Vec3(-halfSize, 0, halfSize)
        ];

        for (let i = 0; i < rayDirections.length; i++) {
            const direction = new Vec3();
            Vec3.transformQuat(direction, rayDirections[i], rotationQuat);
            Vec3.normalize(direction, direction);

            const rayOrigin = targetPos.clone();
            const ray = new geometry.Ray(rayOrigin.x, rayOrigin.y, rayOrigin.z, direction.x, direction.y, direction.z);

            if (PhysicsSystem.instance.raycastClosest(ray, mask, halfSize * 1.5, queryTrigger)) {
                const result = PhysicsSystem.instance.raycastClosestResult;
                const gate = result.collider.node.getComponent(GateController);
                if (gate && !foundGates.has(gate)) {
                    foundGates.add(gate);
                    gates.push(gate);
                }
            }
        }

        return gates.length > 0 ? gates : null;
    }

}


