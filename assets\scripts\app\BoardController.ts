import { _decorator, Component, Node, Vec3, Vec2, math, Texture2D } from 'cc';
import { GameConstantsValue } from '../enums/GameConfig';
import { FloorView } from './FloorView';
import { ObstacleManager } from './ObstacleManager';
import { GateManager } from './GateManager';
import { BlockManager } from './BlockManager';
import { LevelDataConfig } from '../enums/LevelDataConfig';
import { BlockMemeManager } from './BlockMemeManager';
const { ccclass, property } = _decorator;

export interface BoardModel {
    Row: number;
    Col: number;
}


@ccclass('BoardController')
export class BoardController extends Component {

    @property(FloorView)
    floorView: FloorView = null;
    @property(ObstacleManager)
    obstacleManager: ObstacleManager = null;
    @property(GateManager)
    gateManager: GateManager = null;
    @property(BlockManager)
    blockManagers: BlockManager = null;

    private _boardModel: BoardModel;

    private static _instance: BoardController | null = null;

    public static get Instance(): BoardController {
        if (!this._instance) {
            console.error("BoardController instance is not yet available.");
        }
        return this._instance!;
    }

    protected onLoad(): void {

        BoardController._instance = this;
    }

    public initializeBoard(config: LevelDataConfig): void {
        this.clearBoard();

        this._boardModel = {
            Row: config.Row,
            Col: config.Column
        };

        this.floorView.initialize(config);
        this.obstacleManager.initialize(config.Obstacles);
        this.gateManager.initialize(config.Gates);
        this.blockManagers.initialize(config.Elements, this);
    }

    public getCellWorldPosition(row: number, col: number): Vec3 {

        const centerPos = this.getCenterGridPosition();

        const worldX = GameConstantsValue.CellSize * col - centerPos.x;
        const worldY = GameConstantsValue.CellSize * row - centerPos.y;
        return new Vec3(worldX, 0, worldY);
    }

    public getCellFromWorldPosition(position: Vec3): math.Vec2 {

        const centerPos = this.getCenterGridPosition();
        const col = (position.x + centerPos.x) / GameConstantsValue.CellSize;
        const row = (position.z + centerPos.y) / GameConstantsValue.CellSize;

        return new math.Vec2(Math.round(row), Math.round(col));
    }

    private getCenterGridPosition(): Vec2 {
        if (!this._boardModel || typeof this._boardModel.Col !== 'number' || typeof this._boardModel.Row !== 'number') {
            console.error("BoardModel not initialized correctly for getCenterGridPosition.");
            return Vec2.ZERO;
        }

        return new Vec2(
            (this._boardModel.Col - 1) * GameConstantsValue.CellSize / 2,
            (this._boardModel.Row - 1) * GameConstantsValue.CellSize / 2
        );
    }

    public getBoundingRectCenter(arrPos: Vec2[]): Vec3 {
        if (!arrPos || arrPos.length === 0) {
            return Vec3.ZERO;
        }

        const centerPos = this.getCenterGridPosition();

        let firstCell = this.findCellHolder(arrPos[0], centerPos);
        let minX = firstCell.x;
        let maxX = firstCell.x;
        let minY = firstCell.z;
        let maxY = firstCell.z;

        for (let i = 1; i < arrPos.length; i++) {
            const cell = this.findCellHolder(arrPos[i], centerPos);
            if (cell.x < minX) minX = cell.x;
            if (cell.x > maxX) maxX = cell.x;
            if (cell.z < minY) minY = cell.z;
            if (cell.z > maxY) maxY = cell.z;
        }

        const centerX = (minX + maxX) / 2;
        const centerZ = (minY + maxY) / 2;
        return new Vec3(centerX, 0, centerZ);
    }

    private findCellHolder(pos: Vec2, centerPos: Vec2): Vec3 {
        const col = Math.round((pos.x + centerPos.x) / GameConstantsValue.CellSize);
        const row = Math.round((pos.y + centerPos.y) / GameConstantsValue.CellSize);
        return this.getCellWorldPosition(row, col);
    }

    public clearBoard(): void {
        this.floorView?.node?.destroyAllChildren();
        this.obstacleManager?.node?.destroyAllChildren();
        this.gateManager?.node?.destroyAllChildren();
        this.blockManagers?.node?.destroyAllChildren();
    }
}


