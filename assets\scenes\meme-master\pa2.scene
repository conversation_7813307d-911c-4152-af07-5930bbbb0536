[{"__type__": "cc.SceneAsset", "_name": "pa2", "_objFlags": 0, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "pa2", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 8}, {"__id__": 17}, {"__id__": 84}], "_active": true, "_components": [], "_prefab": {"__id__": 228}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 229}, "_id": "6ba8f0a3-374a-48ab-ae2b-912754cedd2c"}, {"__type__": "cc.Node", "_name": "Enviroment", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 6}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "02BvN2/MZMxqfau/UlU/t5"}, {"__type__": "cc.Node", "_name": "Main Light", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": -0.8660254037844386, "y": 0, "z": 0, "w": 0.5000000000000001}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 0.9999999999999999, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": -120, "y": 0, "z": 0}, "_id": "c0y6F5f+pAvI805TdmxIjx"}, {"__type__": "cc.DirectionalLight", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useColorTemperature": false, "_colorTemperature": 8500, "_staticSettings": {"__id__": 5}, "_visibility": -325058561, "_illuminanceHDR": 65000, "_illuminance": 65000, "_illuminanceLDR": 4, "_shadowEnabled": false, "_shadowPcf": 0, "_shadowBias": 1e-05, "_shadowNormalBias": 0, "_shadowSaturation": 1, "_shadowDistance": 50, "_shadowInvisibleOcclusionRange": 200, "_csmLevel": 4, "_csmLayerLambda": 0.75, "_csmOptimizationMode": 2, "_csmAdvancedOptions": false, "_csmLayersTransition": false, "_csmTransitionRange": 0.05, "_shadowFixedArea": false, "_shadowNear": 0.1, "_shadowFar": 10, "_shadowOrthoSize": 5, "_id": "597uMYCbhEtJQc0ffJlcgA"}, {"__type__": "cc.StaticLightSettings", "_baked": false, "_editorOnly": false, "_castShadow": false}, {"__type__": "cc.Node", "_name": "Main Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 7}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 14, "z": 0.7}, "_lrot": {"__type__": "cc.Quat", "x": 4.329780281177466e-17, "y": 0.7071067811865476, "z": 0.7071067811865475, "w": -4.329780281177467e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9999999999999999, "y": 0.9999999999999999, "z": 0.9999999999999999}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": -89.99999999999999, "y": -180, "z": 0}, "_id": "c9DMICJLFO5IeO07EPon7U"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 6}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 0, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 24, "_near": 1, "_far": 1000, "_color": {"__type__": "cc.Color", "r": 24, "g": 30, "b": 99, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 7, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1822425087, "_targetTexture": null, "_cameraType": -1, "_trackingType": 0, "_id": "7dWQTpwS5LrIHnc1zAPUtf"}, {"__type__": "cc.Node", "_name": "GameSystem", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 9}, {"__id__": 11}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "cd7QFsQm9Kx6VRn0Lv11q7"}, {"__type__": "cc.Node", "_name": "GameData", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 8}, "_children": [], "_active": true, "_components": [{"__id__": 10}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d8e7WUny5JPoUeYn52f+q8"}, {"__type__": "1a265gtPM5Ns4UIKBwxfDS1", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": null, "selectedCityID": 1, "_id": "c0K54PYQNA5IexFK99v410"}, {"__type__": "cc.Node", "_name": "GameFlow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 8}, "_children": [{"__id__": 12}, {"__id__": 15}], "_active": true, "_components": [{"__id__": 83}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "bf/OaOZzJP14eP2zDtdm3L"}, {"__type__": "cc.Node", "_name": "GameManager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [{"__id__": 13}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0bguVXkBxCKq82EINnonkz"}, {"__type__": "552b9xV2X1J5YtPvwq1B33I", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": null, "soundManager": {"__id__": 14}, "gameplay": {"__id__": 16}, "inputManager": {"__id__": 19}, "_id": "95VTy6cwdPMKKgfYZ60i2g"}, {"__type__": "b6c059j/LBMRZ+Vt2xEQBus", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": null, "BGM": {"__uuid__": "06d05b0f-8873-4439-842d-9f6acd5549d8", "__expectedType__": "cc.AudioClip"}, "Button_Click": {"__uuid__": "961c172f-47cc-4363-9daa-b84d9054bf39", "__expectedType__": "cc.AudioClip"}, "Clear_Block": {"__uuid__": "c36f9a56-34b5-47a0-887d-e02f73782cd8", "__expectedType__": "cc.AudioClip"}, "Game_Over": {"__uuid__": "43faba26-f489-4ba5-98bc-003c579a87f7", "__expectedType__": "cc.AudioClip"}, "Win": {"__uuid__": "053fb1fb-c6f5-44ee-906c-0ae5c3c7ceac", "__expectedType__": "cc.AudioClip"}, "Level_Complete": {"__uuid__": "7a7bf3de-dd86-433f-b943-b53fd5d23445", "__expectedType__": "cc.AudioClip"}, "Deselect_Block": {"__uuid__": "bbeeb7ac-d14b-4879-979c-baa358028290", "__expectedType__": "cc.AudioClip"}, "Select_Block": {"__uuid__": "a52fa2cd-724b-473b-b8b3-67970c4a8258", "__expectedType__": "cc.AudioClip"}, "Time_Out": {"__uuid__": "2bf34d59-0af8-4c13-b4e5-f68da33de521", "__expectedType__": "cc.AudioClip"}, "Star_Appear": {"__uuid__": "71b6e6a6-c2c6-475e-9404-f03645<PERSON>beed", "__expectedType__": "cc.AudioClip"}, "_id": "bcG03Vn+pIYJxamD2C7lny"}, {"__type__": "cc.Node", "_name": "SoundManager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [{"__id__": 14}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "73xTxpp5ZEs4k5Z2YR2504"}, {"__type__": "bc7bd7iQglAAplcgWgk39ym", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 17}, "_enabled": true, "__prefab": null, "boardController": {"__id__": 42}, "levelManager": {"__id__": 47}, "unlockCity": null, "uiCanvas": null, "_id": "09GdayMJZKVqUe29qdXC5O"}, {"__type__": "cc.Node", "_name": "Gameplay", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 18}, {"__id__": 46}, {"__id__": 21}, {"__id__": 48}, {"__id__": 76}], "_active": true, "_components": [{"__id__": 16}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b3wmtU8xFOvJuOtalHmTb2"}, {"__type__": "cc.Node", "_name": "InputManager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_components": [{"__id__": 19}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b1ZTXj1HFOgZfHO2h3B+VJ"}, {"__type__": "1cb14I7bhVB847R4ovHbCQh", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": null, "plane": {"__id__": 20}, "camera": {"__id__": 7}, "dragSpeed": 50, "maxVelocity": 50, "smoothFactor": 0.8, "dampingFactor": 0.95, "_id": "3egVQGhL5Kzq1Lz5naM/WZ"}, {"__type__": "cc.Node", "_name": "Plane", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 21}, "_children": [], "_active": true, "_components": [{"__id__": 43}, {"__id__": 45}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 1.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 10, "y": 1, "z": 10}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "02WDi7h8FNVoRMe7dyWhDX"}, {"__type__": "cc.Node", "_name": "BoardController", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 17}, "_children": [{"__id__": 20}, {"__id__": 22}, {"__id__": 24}, {"__id__": 30}, {"__id__": 35}], "_active": true, "_components": [{"__id__": 42}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "97w79RpXND8Z3vSUZIriqn"}, {"__type__": "cc.Node", "_name": "Floors", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 21}, "_children": [], "_active": true, "_components": [{"__id__": 23}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "27NzMB4XJHuIhTjlSxtDS3"}, {"__type__": "7f4fbtqlrNErK1rZP7MyPMg", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 22}, "_enabled": true, "__prefab": null, "cellPrefab": {"__uuid__": "28e2702d-c052-4306-be8e-0403194ba88c", "__expectedType__": "cc.Prefab"}, "offsetZ": 0.5, "_id": "fdONqLtrFHVbf26egxqHpT"}, {"__type__": "cc.Node", "_name": "Obstacles", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 21}, "_children": [], "_active": true, "_components": [{"__id__": 25}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ab2wTnIztKGq/1JoXku78y"}, {"__type__": "945e6+P2cdDa5mgNzUUsKtD", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": null, "obstaclePrefabs": [{"__id__": 26}, {"__id__": 27}, {"__id__": 28}, {"__id__": 29}], "_id": "f0UQ3KltJOBZ3aw7lJyBpZ"}, {"__type__": "ObstaclePrefabItem", "type": 0, "prefab": {"__uuid__": "8ecd179b-40e6-4003-a190-cdaa47c99326", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObstaclePrefabItem", "type": 1, "prefab": {"__uuid__": "9ca8097d-9180-492b-98b8-1cf9c2150f36", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObstaclePrefabItem", "type": 2, "prefab": {"__uuid__": "97abf1e8-ca46-42b7-81d3-226667b9cbfe", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObstaclePrefabItem", "type": 3, "prefab": {"__uuid__": "8a6a2d09-720a-4b5d-bb0d-af68fa557f76", "__expectedType__": "cc.Prefab"}}, {"__type__": "cc.Node", "_name": "Gates", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 21}, "_children": [], "_active": true, "_components": [{"__id__": 31}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5fDNFxbWtDpozpkm+6oEQ1"}, {"__type__": "f48f7JNskxCj4xFXsZywuiw", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 30}, "_enabled": true, "__prefab": null, "gatePrefabs": [{"__id__": 32}, {"__id__": 33}, {"__id__": 34}], "_id": "cbr5+gS5dK9KKOPoxbi9vy"}, {"__type__": "GatePrefabMapping", "gateType": 1, "prefab": {"__uuid__": "d848b069-2aa6-428f-8af3-2593c32d7f85", "__expectedType__": "cc.Prefab"}}, {"__type__": "GatePrefabMapping", "gateType": 2, "prefab": {"__uuid__": "76b7b698-5492-46b3-962d-f094883d0d57", "__expectedType__": "cc.Prefab"}}, {"__type__": "GatePrefabMapping", "gateType": 3, "prefab": {"__uuid__": "07addf65-b572-4f31-a42d-6ca954952bc0", "__expectedType__": "cc.Prefab"}}, {"__type__": "cc.Node", "_name": "Elements", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 21}, "_children": [], "_active": true, "_components": [{"__id__": 36}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "fdfAiO5NFJlplCKF0hsFIL"}, {"__type__": "2ef56yheppPoLUtrzohXwVZ", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": null, "elementPrefabs": [{"__id__": 37}, {"__id__": 38}, {"__id__": 39}, {"__id__": 40}, {"__id__": 41}], "offsetZ": 1, "_id": "6fRPmOL9pOZZLy/YxLZV4d"}, {"__type__": "ElementPrefabMapping", "shapeName": 1, "prefab": {"__uuid__": "8e789533-0ac5-45f3-aaf1-579ce261bbfc", "__expectedType__": "cc.Prefab"}}, {"__type__": "ElementPrefabMapping", "shapeName": 2, "prefab": {"__uuid__": "7c7ee679-3c2a-4185-8a73-36d438b2142d", "__expectedType__": "cc.Prefab"}}, {"__type__": "ElementPrefabMapping", "shapeName": 3, "prefab": {"__uuid__": "fef66d29-5a07-4f40-9d52-3f3528b7201e", "__expectedType__": "cc.Prefab"}}, {"__type__": "ElementPrefabMapping", "shapeName": 6, "prefab": {"__uuid__": "9f94c442-7504-4669-af50-7fd7a4e276fe", "__expectedType__": "cc.Prefab"}}, {"__type__": "ElementPrefabMapping", "shapeName": 8, "prefab": {"__uuid__": "f5b6bb7f-07c3-432d-b403-b7cb369eb090", "__expectedType__": "cc.Prefab"}}, {"__type__": "c9fbcTgZv5Pv43kazZvee+h", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": null, "floorView": {"__id__": 23}, "obstacleManager": {"__id__": 25}, "gateManager": {"__id__": 31}, "blockManagers": {"__id__": 36}, "_id": "51Kqp4+GNFu6UsVD08/3Zv"}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": false, "__prefab": null, "_materials": [], "_visFlags": 0, "bakeSettings": {"__id__": 44}, "_mesh": {"__uuid__": "1263d74c-8167-4928-91a6-4e2672411f47@2e76e", "__expectedType__": "cc.Mesh"}, "_shadowCastingMode": 0, "_shadowReceivingMode": 1, "_shadowBias": 0, "_shadowNormalBias": 0, "_reflectionProbeId": -1, "_enableMorph": true, "_id": "eeh9Jo9xFCz6oRqI9DJfBX"}, {"__type__": "cc.ModelBakeSettings", "texture": null, "uvParam": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_bakeable": false, "_castShadow": false, "_receiveShadow": false, "_recieveShadow": false, "_lightmapSize": 64, "_useLightProbe": false, "_bakeToLightProbe": true, "_reflectionProbeType": 0, "_bakeToReflectionProbe": true, "_probeCubemap": null}, {"__type__": "<PERSON>.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "_material": null, "_isTrigger": true, "_center": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_mesh": {"__uuid__": "1263d74c-8167-4928-91a6-4e2672411f47@2e76e", "__expectedType__": "cc.Mesh"}, "_convex": false, "_id": "b5XLoac4ZGs6UKc5Wj9tEn"}, {"__type__": "cc.Node", "_name": "LevelManager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_components": [{"__id__": 47}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8eY8814HFEBq2zkQxY4A+j"}, {"__type__": "d0506x5PIJMPJWOoEd/FVRO", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 46}, "_enabled": true, "__prefab": null, "jsonData": {"__uuid__": "db45f6f9-71ab-416a-a3de-4d2c957d60fb", "__expectedType__": "cc.Json<PERSON>set"}, "_id": "0e7CHSPtdOtKQeZf1Qtxui"}, {"__type__": "cc.Node", "_name": "ColorConfigs", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_components": [{"__id__": 49}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4cP9MzwgdDsIPvXnrNFBDX"}, {"__type__": "91ef5kEJkNAo4LG6h86zRND", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 48}, "_enabled": true, "__prefab": null, "colorMappings": [{"__id__": 50}, {"__id__": 51}, {"__id__": 52}, {"__id__": 53}, {"__id__": 54}, {"__id__": 55}, {"__id__": 56}, {"__id__": 57}, {"__id__": 58}, {"__id__": 59}], "memeMappings": [{"__id__": 60}, {"__id__": 61}, {"__id__": 62}, {"__id__": 63}, {"__id__": 64}, {"__id__": 65}, {"__id__": 66}, {"__id__": 67}, {"__id__": 68}, {"__id__": 69}], "memeSpriteMappings": [{"__id__": 70}, {"__id__": 71}, {"__id__": 72}, {"__id__": 73}, {"__id__": 74}, {"__id__": 75}], "outlineMaterial": {"__uuid__": "b61f823b-4514-47db-a023-4fdc70f836cd", "__expectedType__": "cc.Material"}, "originalMaterial": {"__uuid__": "fe48ed51-f544-4266-989b-48087860ed89", "__expectedType__": "cc.Material"}, "_id": "ebRd79MgVH35czy5zYdSaL"}, {"__type__": "ColorMapping", "colorType": 11, "texture": {"__uuid__": "fdb9a4e8-1809-4ed9-8ba7-1ebcf6b71fff@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "ColorMapping", "colorType": 1, "texture": {"__uuid__": "1cec19fb-3b48-4008-ac77-f82721f62d19@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "ColorMapping", "colorType": 2, "texture": {"__uuid__": "5f075cd5-462f-4b0b-aa3f-9447a9965ada@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "ColorMapping", "colorType": 3, "texture": {"__uuid__": "f5efd4dc-f19e-40d5-905e-b046bdd86b8a@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "ColorMapping", "colorType": 4, "texture": {"__uuid__": "82ba8c3f-ad50-4031-b2fe-47ae3f3890a3@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "ColorMapping", "colorType": 5, "texture": {"__uuid__": "3033b392-8314-4ef1-813b-7f75b2e8a562@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "ColorMapping", "colorType": 6, "texture": {"__uuid__": "7f69600c-0fff-452c-9cbc-87c35d1ac904@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "ColorMapping", "colorType": 7, "texture": {"__uuid__": "4d026fae-9902-464f-ab33-bbefbdc56c68@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "ColorMapping", "colorType": 8, "texture": {"__uuid__": "953e1886-7d40-4832-86cc-c1accbceaf75@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "ColorMapping", "colorType": 9, "texture": {"__uuid__": "2e52ba89-42a6-4c72-9ad8-d6ebfc40d902@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "MemeMapping", "colorType": 11, "texture": {"__uuid__": "32cebe01-394d-4311-90a0-fd8c0de5667a@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "MemeMapping", "colorType": 1, "texture": null}, {"__type__": "MemeMapping", "colorType": 2, "texture": {"__uuid__": "b08f0eee-0cec-4e42-96bc-8574ffd0402a@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "MemeMapping", "colorType": 3, "texture": {"__uuid__": "614b8581-51e4-4d0e-82d5-d2b4f9d94282@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "MemeMapping", "colorType": 4, "texture": {"__uuid__": "4d3d83e3-6836-4801-9be2-367a15dbc2ab@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "MemeMapping", "colorType": 5, "texture": {"__uuid__": "5a8f2dda-ccf1-4eb7-9f01-59c791f4471f@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "MemeMapping", "colorType": 6, "texture": null}, {"__type__": "MemeMapping", "colorType": 7, "texture": {"__uuid__": "21733b46-90ae-4f8e-ae74-2e8a5d5b6224@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "MemeMapping", "colorType": 8, "texture": null}, {"__type__": "MemeMapping", "colorType": 9, "texture": null}, {"__type__": "MemeFrameMapping", "colorType": 11, "spriteFrame": {"__uuid__": "32cebe01-394d-4311-90a0-fd8c0de5667a@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "MemeFrameMapping", "colorType": 2, "spriteFrame": {"__uuid__": "b08f0eee-0cec-4e42-96bc-8574ffd0402a@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "MemeFrameMapping", "colorType": 7, "spriteFrame": {"__uuid__": "21733b46-90ae-4f8e-ae74-2e8a5d5b6224@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "MemeFrameMapping", "colorType": 3, "spriteFrame": {"__uuid__": "614b8581-51e4-4d0e-82d5-d2b4f9d94282@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "MemeFrameMapping", "colorType": 4, "spriteFrame": {"__uuid__": "4d3d83e3-6836-4801-9be2-367a15dbc2ab@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "MemeFrameMapping", "colorType": 5, "spriteFrame": {"__uuid__": "5a8f2dda-ccf1-4eb7-9f01-59c791f4471f@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 17}, "_children": [{"__id__": 77}, {"__id__": 80}], "_active": false, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "13dPUbbQlHK4Jt8jWl0ItD"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 76}, "_children": [], "_active": true, "_components": [{"__id__": 78}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -1, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": -7.703719777548943e-34, "y": -1, "z": 4.717167883551737e-50, "w": 6.123233995736766e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 4.5, "y": 0.9999999999999999, "z": 6.5}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": -180, "z": 8.827812596100317e-32}, "_id": "bfL6IwtXFNjq/s4a2y4I6d"}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "Plane<ModelComponent>", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 77}, "_enabled": true, "__prefab": null, "_materials": [{"__uuid__": "10825948-ef96-4d95-9a66-e594e880423d", "__expectedType__": "cc.Material"}], "_visFlags": 0, "bakeSettings": {"__id__": 79}, "_mesh": {"__uuid__": "1263d74c-8167-4928-91a6-4e2672411f47@2e76e", "__expectedType__": "cc.Mesh"}, "_shadowCastingMode": 0, "_shadowReceivingMode": 1, "_shadowBias": 0, "_shadowNormalBias": 0, "_reflectionProbeId": -1, "_enableMorph": true, "_id": "efP568Xz9MIYimEVt8t55h"}, {"__type__": "cc.ModelBakeSettings", "texture": null, "uvParam": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_bakeable": false, "_castShadow": false, "_receiveShadow": false, "_recieveShadow": false, "_lightmapSize": 64, "_useLightProbe": false, "_bakeToLightProbe": true, "_reflectionProbeType": 0, "_bakeToReflectionProbe": true, "_probeCubemap": null}, {"__type__": "cc.Node", "_name": "bg-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 76}, "_children": [], "_active": false, "_components": [{"__id__": 81}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -8, "y": -1, "z": 8}, "_lrot": {"__type__": "cc.Quat", "x": -3.851859888774472e-34, "y": -1, "z": 2.3585839417758684e-50, "w": 6.123233995736766e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 6.5, "y": 0.9999999999999997, "z": 6.5}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": -180, "z": 4.4139062980501586e-32}, "_id": "21Q8cF5WRHpYyYie1h4Y3U"}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "Plane<ModelComponent>", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 80}, "_enabled": true, "__prefab": null, "_materials": [{"__uuid__": "10825948-ef96-4d95-9a66-e594e880423d", "__expectedType__": "cc.Material"}], "_visFlags": 0, "bakeSettings": {"__id__": 82}, "_mesh": {"__uuid__": "1263d74c-8167-4928-91a6-4e2672411f47@2e76e", "__expectedType__": "cc.Mesh"}, "_shadowCastingMode": 0, "_shadowReceivingMode": 1, "_shadowBias": 0, "_shadowNormalBias": 0, "_reflectionProbeId": -1, "_enableMorph": true, "_id": "fdBDHLNxBPT71x6BQgphuP"}, {"__type__": "cc.ModelBakeSettings", "texture": null, "uvParam": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_bakeable": false, "_castShadow": false, "_receiveShadow": false, "_recieveShadow": false, "_lightmapSize": 64, "_useLightProbe": false, "_bakeToLightProbe": true, "_reflectionProbeType": 0, "_bakeToReflectionProbe": true, "_probeCubemap": null}, {"__type__": "67090FWWv9IBaK0NzXmcxUG", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": null, "gameManager": {"__id__": 13}, "_id": "e1u13cNGlIL7uHHMkGz5CX"}, {"__type__": "cc.Node", "_name": "UICanvas", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 85}, {"__id__": 87}, {"__id__": 91}, {"__id__": 167}, {"__id__": 171}, {"__id__": 213}], "_active": true, "_components": [{"__id__": 225}, {"__id__": 226}, {"__id__": 227}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 540, "y": 960, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e0L5NXnVlFvondS2CtVgDD"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "_parent": {"__id__": 84}, "_children": [], "_active": true, "_components": [{"__id__": 86}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "deSI/+2OdMlbBVJhQZnFTV"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "node": {"__id__": 85}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 1073741824, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 960, "_near": 1, "_far": 2000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 6, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 41943040, "_targetTexture": null, "_cameraType": -1, "_trackingType": 0, "_id": "c9f4Fefg1BNJ2RiXvj+fhj"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "_parent": {"__id__": 84}, "_children": [], "_active": false, "_components": [{"__id__": 88}, {"__id__": 89}, {"__id__": 90}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "59g4IO4elFOqm3gnpS/+0H"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 960}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f4zoMv0c1OeZNyXS8+RAid"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e0f4cbcd-4986-4a86-9819-7cfba8b52733@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": "ea1HlLDSlIAq+fLMlJXfhV"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_alignMode": 2, "_lockFlags": 45, "_id": "ad9wO7vndAU4QHIL1DE8kl"}, {"__type__": "cc.Node", "_name": "TopUi", "_objFlags": 0, "_parent": {"__id__": 84}, "_children": [{"__id__": 92}, {"__id__": 96}, {"__id__": 112}], "_active": true, "_components": [{"__id__": 165}, {"__id__": 166}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 822.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "91h9pDXqFJrq8C7KSSnlLd"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "_parent": {"__id__": 91}, "_children": [], "_active": true, "_components": [{"__id__": 93}, {"__id__": 94}, {"__id__": 95}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "dfJegmmM1Px4HKL/evicJf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 92}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 275}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "2131LQAXVGJYfGXX9NLHA9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 92}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "dcf5fb7b-2469-423e-a184-1dcce3c6e02e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "7cRd2PKI1Gf79DwzCub5Ow"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 92}, "_enabled": true, "__prefab": null, "_alignFlags": 40, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 46, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "1a4vUMNmtMLoiXRkrzJTOW"}, {"__type__": "cc.Node", "_name": "Move", "_objFlags": 0, "_parent": {"__id__": 91}, "_children": [{"__id__": 97}, {"__id__": 104}], "_active": true, "_components": [{"__id__": 108}, {"__id__": 109}, {"__id__": 110}, {"__id__": 111}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -309.3025, "y": 3.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3eR7gQnhRNI5jeNiTXaWLy"}, {"__type__": "cc.Node", "_name": "Ui_BgText", "_objFlags": 0, "_parent": {"__id__": 96}, "_children": [{"__id__": 98}], "_active": true, "_components": [{"__id__": 102}, {"__id__": 103}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 1.9645000000000001, "y": 62.302, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ccIt32qYJO0rUjHgDjgpWm"}, {"__type__": "cc.Node", "_name": "Label-001", "_objFlags": 0, "_parent": {"__id__": 97}, "_children": [], "_active": true, "_components": [{"__id__": 99}, {"__id__": 100}, {"__id__": 101}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 13.212, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b6ggCd59VJ9KiP4IvzQr1I"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e1w5DqIz1ATKr8SfL+evey"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Moves", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": true, "_font": {"__uuid__": "00c91cf0-7aec-4676-9aad-7f7d7db695b8", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 2, "_id": "11u3xEJNpJl5evciIeruBE"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_width": 2, "_id": "ccRch1LF5FOq3iPwocUx+d"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 97}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 203.929, "height": 47.483}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "07HDVZJkVK5rTgKqQrqMc+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 97}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5a69d0fa-f626-4732-9033-673980e9353c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "96e7wbpodJ5J0LkxKm1HD9"}, {"__type__": "cc.Node", "_name": "Label-001", "_objFlags": 0, "_parent": {"__id__": 96}, "_children": [], "_active": true, "_components": [{"__id__": 105}, {"__id__": 106}, {"__id__": 107}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -13.909, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "369d9LmopEbJhV16wdd2ce"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 104}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c6t052Ov5L4pHGi97CTKTn"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 104}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "100", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 80, "_fontSize": 80, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 80, "_overflow": 1, "_enableWrapText": true, "_font": {"__uuid__": "00c91cf0-7aec-4676-9aad-7f7d7db695b8", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 2, "_id": "eaaoBnkc1BCJZEoczY6EpV"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 104}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_width": 2, "_id": "73bnC5S/tFw7ntRlpeiQHs"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 96}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 237.395, "height": 172}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e7HzfLUAlEIJBL8wNId6mO"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 96}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fbcffec7-09a6-49bb-b73b-b584971de887@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "7dfMNrFDJK952TRfwSYfsk"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 96}, "_enabled": true, "__prefab": null, "_alignFlags": 9, "_target": null, "_left": 111.99999999999999, "_right": 0, "_top": 48, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 1, "_id": "dbRQ84EE9EdL9PzJ1sRp6k"}, {"__type__": "3219dGhzOBOu6l8wur+OLiZ", "_name": "", "_objFlags": 0, "node": {"__id__": 96}, "_enabled": true, "__prefab": null, "maxMoveCount": 60, "lblMoveCount": {"__id__": 106}, "_id": "77SvvhPv1NDIqLVbeAvlxb"}, {"__type__": "cc.Node", "_name": "Level", "_objFlags": 0, "_parent": {"__id__": 91}, "_children": [{"__id__": 113}, {"__id__": 120}], "_active": true, "_components": [{"__id__": 161}, {"__id__": 162}, {"__id__": 163}, {"__id__": 164}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 133.83900000000006, "y": 3.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "85kcuHnX1FwrLsezAq9D75"}, {"__type__": "cc.Node", "_name": "Ui_BgText", "_objFlags": 0, "_parent": {"__id__": 112}, "_children": [{"__id__": 114}], "_active": true, "_components": [{"__id__": 118}, {"__id__": 119}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 62.302, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8fRV5KtiNI5Z9tte77gfQN"}, {"__type__": "cc.Node", "_name": "Label-001", "_objFlags": 0, "_parent": {"__id__": 113}, "_children": [], "_active": true, "_components": [{"__id__": 115}, {"__id__": 116}, {"__id__": 117}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 13.212, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "09w8Hxfz5OwZSFf2MmETpq"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8am8HMnPJKQJsWVB64Kgvx"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Level", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": true, "_font": {"__uuid__": "00c91cf0-7aec-4676-9aad-7f7d7db695b8", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 2, "_id": "4dICl9NmpECqYYUnTELW4G"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_width": 2, "_id": "4ejhf6etZLBZGmN+ebzinL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 113}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 47.483}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9c7rmhl2NMdJgGyP7zZi+o"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 113}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5a69d0fa-f626-4732-9033-673980e9353c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "91OE9Pm6RJjIZE/Mgp+7d9"}, {"__type__": "cc.Node", "_name": "Collection", "_objFlags": 0, "_parent": {"__id__": 112}, "_children": [{"__id__": 121}, {"__id__": 129}, {"__id__": 139}, {"__id__": 149}], "_active": true, "_components": [{"__id__": 159}, {"__id__": 160}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -18, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f2lXcBIXVND6abC4NSNorP"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 120}, "_prefab": {"__id__": 122}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 121}, "asset": {"__uuid__": "936355d5-a555-4cad-bf7c-1e59c36e16a2", "__expectedType__": "cc.Prefab"}, "fileId": "b2/egaZvBFSKa7IUfgtRLa", "instance": {"__id__": 123}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "3azZmsmgJJN7Nyjo58G/va", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 124}, {"__id__": 126}, {"__id__": 127}, {"__id__": 128}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 125}, "propertyPath": ["_name"], "value": "GemMeme"}, {"__type__": "cc.TargetInfo", "localID": ["b2/egaZvBFSKa7IUfgtRLa"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 125}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -198, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 125}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 125}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_name": "GemMeme-001", "_objFlags": 0, "_parent": {"__id__": 120}, "_children": [{"__id__": 130}, {"__id__": 133}], "_active": true, "_components": [{"__id__": 137}, {"__id__": 138}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -66, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "484DnR94BPt7rV8YmR16Fe"}, {"__type__": "cc.Node", "_name": "icon", "_objFlags": 0, "_parent": {"__id__": 129}, "_children": [], "_active": true, "_components": [{"__id__": 131}, {"__id__": 132}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d0QfcBUnlFkrB7iysS9avH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 130}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "77Dm7z2YNKxoBGxb7B8veD"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 130}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "32cebe01-394d-4311-90a0-fd8c0de5667a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "3aapbfHepAo5qM6JUxBA8k"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 129}, "_children": [], "_active": true, "_components": [{"__id__": 134}, {"__id__": 135}, {"__id__": 136}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 40.75, "y": -31.87, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "824Ik8MONKR5U6ZumRgp+T"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 133}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a8z+1/TUxJ5qYwBz8C8oKL"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 133}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "5", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": true, "_font": {"__uuid__": "00c91cf0-7aec-4676-9aad-7f7d7db695b8", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 2, "_id": "4ay8wXmfpMOooXn15zuMjk"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 133}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_width": 2, "_id": "3eOIjqculPM5oo1EcniRms"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 129}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "46tY8EuVxKgr8V80x9w2El"}, {"__type__": "c6598cqg9xP7oSUIzrIhZHD", "_name": "", "_objFlags": 0, "node": {"__id__": 129}, "_enabled": true, "__prefab": null, "icon": {"__id__": 132}, "lblAmount": {"__id__": 135}, "_id": "92o4+WTsxGvonlZmg4U2kI"}, {"__type__": "cc.Node", "_name": "GemMeme-002", "_objFlags": 0, "_parent": {"__id__": 120}, "_children": [{"__id__": 140}, {"__id__": 143}], "_active": true, "_components": [{"__id__": 147}, {"__id__": 148}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 66, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "acvnyE+6FM6bYySYLUmlBA"}, {"__type__": "cc.Node", "_name": "icon", "_objFlags": 0, "_parent": {"__id__": 139}, "_children": [], "_active": true, "_components": [{"__id__": 141}, {"__id__": 142}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "805wU616VKs7PjeETeKHhA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 140}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "31+KDd8OdIv5bxp8ikgVaS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 140}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "32cebe01-394d-4311-90a0-fd8c0de5667a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "85lBo1IzlFApa3Ghaen81Y"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 139}, "_children": [], "_active": true, "_components": [{"__id__": 144}, {"__id__": 145}, {"__id__": 146}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 40.75, "y": -31.87, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "68gAkv0I5MgKnMGSJidpMh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 143}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ddNDpNcoNObo7vCHHOuQDQ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 143}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "5", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": true, "_font": {"__uuid__": "00c91cf0-7aec-4676-9aad-7f7d7db695b8", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 2, "_id": "63EvD/cq5BBI8wFsPhvS3p"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 143}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_width": 2, "_id": "f3hiHNxkJDVq++BlFSMfoO"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 139}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "67329MLuBPS5EVlcQ/gzBi"}, {"__type__": "c6598cqg9xP7oSUIzrIhZHD", "_name": "", "_objFlags": 0, "node": {"__id__": 139}, "_enabled": true, "__prefab": null, "icon": {"__id__": 142}, "lblAmount": {"__id__": 145}, "_id": "5cO9pfWtNLJJpf0RWgZZdE"}, {"__type__": "cc.Node", "_name": "GemMeme-003", "_objFlags": 0, "_parent": {"__id__": 120}, "_children": [{"__id__": 150}, {"__id__": 153}], "_active": true, "_components": [{"__id__": 157}, {"__id__": 158}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 198, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9e17LhgMFN+LWySDyaDnN2"}, {"__type__": "cc.Node", "_name": "icon", "_objFlags": 0, "_parent": {"__id__": 149}, "_children": [], "_active": true, "_components": [{"__id__": 151}, {"__id__": 152}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "13Xtm0qH5EMqVt3Pledi14"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 150}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d9U0z0jrBFk5/QPBoFq5lx"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 150}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "32cebe01-394d-4311-90a0-fd8c0de5667a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "94IQb6IIFOJp5VvIsPfkLA"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 149}, "_children": [], "_active": true, "_components": [{"__id__": 154}, {"__id__": 155}, {"__id__": 156}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 40.75, "y": -31.87, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2ftHZEdcVFZJuxwMI9OQqw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "71e0DmlDNAUaTZGEU/vjUY"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "5", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": true, "_font": {"__uuid__": "00c91cf0-7aec-4676-9aad-7f7d7db695b8", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 2, "_id": "3cy5yoR1xAlIxlblrcChnI"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_width": 2, "_id": "a2Lqu1gwNAd4ieA/DhbGtF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 149}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "66AwtgU05MA6v9ZsTW+9xc"}, {"__type__": "c6598cqg9xP7oSUIzrIhZHD", "_name": "", "_objFlags": 0, "node": {"__id__": 149}, "_enabled": true, "__prefab": null, "icon": {"__id__": 152}, "lblAmount": {"__id__": 155}, "_id": "4f4Req7qRJMIcMP5faPGa9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 120}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 496, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "2cle/QjdhJr6GXbVCyWA4E"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 120}, "_enabled": true, "__prefab": null, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 32, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "c3TCA6qUVJ4b/e7F8q7f2t"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 112}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 588.3219999999999, "height": 172}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "764WUre3NPQIp5437orUfV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 112}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fbcffec7-09a6-49bb-b73b-b584971de887@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "8f/4ULlhJCkLC/x4WNTUbH"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 112}, "_enabled": true, "__prefab": null, "_alignFlags": 41, "_target": null, "_left": 379.6780000000001, "_right": 112, "_top": 48, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 642.9849999999999, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 1, "_id": "2buNKby/pH3I8o4eg3fRSZ"}, {"__type__": "d9123hPqBJDIohz8L6uEGOS", "_name": "", "_objFlags": 0, "node": {"__id__": 112}, "_enabled": true, "__prefab": null, "content": {"__id__": 120}, "memePrefab": {"__uuid__": "936355d5-a555-4cad-bf7c-1e59c36e16a2", "__expectedType__": "cc.Prefab"}, "levelMemeData": {"__uuid__": "40a63baa-d6e4-404d-89ef-d3dbd2c1af68", "__expectedType__": "cc.Json<PERSON>set"}, "level": 1, "_id": "12NTKR6uJOmIHFwkq+7+Rk"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 91}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 275}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "2aFtH+ArNN9pPCVAJ5ZRUC"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 91}, "_enabled": true, "__prefab": null, "_alignFlags": 1, "_target": null, "_left": 490, "_right": 490, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 1, "_id": "eeLMF3bXZDkLbYvzg4Kzyq"}, {"__type__": "cc.Node", "_name": "ContentMemeUI", "_objFlags": 0, "_parent": {"__id__": 84}, "_children": [], "_active": true, "_components": [{"__id__": 168}, {"__id__": 169}, {"__id__": 170}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "49+ivp7FxHzJIP7SNJD+fj"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 167}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "bbNCxpGR1O76JDbPxobV1I"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 167}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "f8EkKgvDNJtriSGaLQCN3j"}, {"__type__": "9a853/axgJKToDv5HG0J0g3", "_name": "", "_objFlags": 0, "node": {"__id__": 167}, "_enabled": true, "__prefab": null, "camera": {"__id__": 7}, "memePrefab": {"__uuid__": "1c6a8220-734e-46a6-890c-8434992a7803", "__expectedType__": "cc.Prefab"}, "levelMemeUI": {"__id__": 164}, "_id": "d3YHcMO3ZOTrHuO+I6SCVP"}, {"__type__": "cc.Node", "_name": "EndScreen", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 84}, "_children": [{"__id__": 172}, {"__id__": 178}, {"__id__": 189}, {"__id__": 197}], "_active": true, "_components": [{"__id__": 208}, {"__id__": 209}, {"__id__": 210}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "89EG/AjMpJUoW57jTqLng7"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "_parent": {"__id__": 171}, "_children": [], "_active": true, "_components": [{"__id__": 173}, {"__id__": 174}, {"__id__": 175}, {"__id__": 176}, {"__id__": 177}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "191N2XFOpPAYy+WLRAMph7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "31YQGl16hA26Y2nNk+l8LM"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 24, "g": 24, "b": 24, "a": 200}, "_spriteFrame": {"__uuid__": "e44842dd-fc35-4edd-bacf-66acfa6fd311@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "a6lh97s4RAwInOcSJpHo0X"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_alignMode": 2, "_lockFlags": 0, "_id": "5fnCk0UyxKBpe+0a34fTvb"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "__prefab": null, "_opacity": 255, "_id": "5cByrJSb5DDbm6Wstr168W"}, {"__type__": "8c2e3Bq3+tHbIhK6acZcV17", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "__prefab": null, "_id": "044MoO3pNHWICA1vjQzybZ"}, {"__type__": "cc.Node", "_name": "Btn_Play", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 171}, "_children": [{"__id__": 179}], "_active": true, "_components": [{"__id__": 183}, {"__id__": 184}, {"__id__": 185}, {"__id__": 186}, {"__id__": 187}, {"__id__": 188}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -795, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1.2}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "52gIdjkgdLpZsYZOuRIpOh"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 178}, "_children": [], "_active": true, "_components": [{"__id__": 180}, {"__id__": 181}, {"__id__": 182}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 6, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1cWiJa2sVBta+FdgrwK3/4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 179}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 271.04, "height": 83.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "cci3+eCihGHqWgDnuGr601"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 179}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Play Now", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 60, "_fontSize": 60, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 60, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "d10be190-c4b3-4ce6-8a85-4fe22bdeb875", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "e587vWnplB/avXHLxSbCqg"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 179}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 33, "g": 36, "b": 112, "a": 255}, "_width": 4, "_id": "3buUka/rNHE4gTUQ4bRD7/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 178}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 360, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f7PhTwrg5FqpesCWvSDMGG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 178}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fabade8d-544f-4cd7-89cb-0fa3b65145dd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "cae2LSvcdLf5MNK7hm2eQG"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 178}, "_enabled": true, "__prefab": null, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 100, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 4, "_id": "36q9TL89JGAbh72fcpDsFD"}, {"__type__": "8c2e3Bq3+tHbIhK6acZcV17", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 178}, "_enabled": true, "__prefab": null, "_id": "47VVYOdoBIUp2nr4wQzbIr"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 178}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "fabade8d-544f-4cd7-89cb-0fa3b65145dd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": "b27aTyf2tD6Z+Z/XSE05no"}, {"__type__": "961aeGllcFJcaHP0vKDnBY9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 178}, "_enabled": true, "__prefab": null, "ctaNode": {"__id__": 178}, "scaleAmount": 1.1, "duration": 0.5, "useBackEasing": true, "_id": "43SpFfs/9BspXnR/4qI7Fb"}, {"__type__": "cc.Node", "_name": "Content", "_objFlags": 0, "_parent": {"__id__": 171}, "_children": [{"__id__": 190}], "_active": true, "_components": [{"__id__": 195}, {"__id__": 196}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "bdMaZvaaRNSo/OQ6qGyml2"}, {"__type__": "cc.Node", "_name": "containLogo", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 189}, "_children": [{"__id__": 191}], "_active": true, "_components": [{"__id__": 194}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 84.17399999999998, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a28nmnx+1FOpqajUNVkcb3"}, {"__type__": "cc.Node", "_name": "logo", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 190}, "_children": [], "_active": true, "_components": [{"__id__": 192}, {"__id__": 193}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 2, "y": 2, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "90zsYeQmRALb/gqItOi3Wc"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 191}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 368, "height": 277}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "61svnB691DsLiKmjKvzNME"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 191}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3dfe758d-4f19-46ad-b623-2d601ebc08e2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "56XQ8fwDtEzpO2wx4QpZ6+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 190}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "52/n4qyilC26qLW7VO062S"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 189}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "36lstC5vpHdLl3fKR5fpxc"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 189}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "85lCyMCuVLZopbFI/UPs9q"}, {"__type__": "cc.Node", "_name": "Btn_TryAgain", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 171}, "_children": [{"__id__": 198}], "_active": false, "_components": [{"__id__": 202}, {"__id__": 203}, {"__id__": 204}, {"__id__": 205}, {"__id__": 206}, {"__id__": 207}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -795, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1.2}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a7nD+U4CRNQZK+fX/lxa6d"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 197}, "_children": [], "_active": true, "_components": [{"__id__": 199}, {"__id__": 200}, {"__id__": 201}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 6, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "04rePBURJL67NQDrMfluCw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 198}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 274.58, "height": 83.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "01hVz2JzlIrpGCzEzLtaZB"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 198}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Try Again", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 60, "_fontSize": 60, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 60, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "d10be190-c4b3-4ce6-8a85-4fe22bdeb875", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "8epaRmfbFCv7tNYxTWd5Hd"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 198}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 112, "g": 77, "b": 33, "a": 255}, "_width": 4, "_id": "73ki459Z5NDpFcZukRM/+/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 197}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 360, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b8tSvQ+3BLVK4RlaSI+YUe"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 197}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "cbcb2ea2-3a3b-4f9e-b1d1-98ac1e05b121@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "2fu8k8LupB9YnmDgMN3OqE"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 197}, "_enabled": true, "__prefab": null, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 100, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 4, "_id": "d7gVRL9+ZDTodsdZbQLRt3"}, {"__type__": "8c2e3Bq3+tHbIhK6acZcV17", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 197}, "_enabled": true, "__prefab": null, "_id": "c8QLR0duhJH5JFHWSGmJMO"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 197}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": "3aW1sfNPRFJrc6SEsX5Htb"}, {"__type__": "961aeGllcFJcaHP0vKDnBY9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 197}, "_enabled": true, "__prefab": null, "ctaNode": {"__id__": 197}, "scaleAmount": 1.1, "duration": 0.5, "useBackEasing": true, "_id": "f5uKLu0q9OTJ9N/0UAYe0C"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 171}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "71fJFHW5NHUrmijucyBPAf"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 171}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "63dTbeWeJNqaMMiXwhCC89"}, {"__type__": "4a7330/qaxOYYhk1GgffBSs", "_name": "", "_objFlags": 0, "node": {"__id__": 171}, "_enabled": true, "__prefab": null, "uiOpacity": {"__id__": 176}, "content": {"__id__": 189}, "btnCTA": {"__id__": 186}, "btnCTAs": {"__id__": 211}, "btnTryAgain": {"__id__": 205}, "gameplay": {"__id__": 213}, "_id": "47ut9aUSpAupPxvSginy6o"}, {"__type__": "8c2e3Bq3+tHbIhK6acZcV17", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": null, "_id": "80UPo7VLZLkrs4WuRDJj5q"}, {"__type__": "cc.Node", "_name": "Btn_Play", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 213}, "_children": [{"__id__": 216}], "_active": true, "_components": [{"__id__": 220}, {"__id__": 221}, {"__id__": 222}, {"__id__": 211}, {"__id__": 223}, {"__id__": 224}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -795, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1.2}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "58cvOef3ZMQIgUYpBtTCFE"}, {"__type__": "cc.Node", "_name": "Gameplay", "_objFlags": 0, "_parent": {"__id__": 84}, "_children": [{"__id__": 212}], "_active": true, "_components": [{"__id__": 214}, {"__id__": 215}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "16KTsPstlI6J73ZhuOL/A2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 213}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "5ae6kmjglCOJ8NsrCLQTzE"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 213}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "baNn6L+q9Ko58hYxJ/r6zy"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 212}, "_children": [], "_active": true, "_components": [{"__id__": 217}, {"__id__": 218}, {"__id__": 219}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 6, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a20d+3s91H3aoEE+io7bJ7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 216}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 271.04, "height": 83.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "687fa9hNBBZ6mE/ryl7Gut"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 216}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Play Now", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 60, "_fontSize": 60, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 60, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "d10be190-c4b3-4ce6-8a85-4fe22bdeb875", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "54fhm/rTNEw4ITAMTRaYN6"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 216}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 33, "g": 36, "b": 112, "a": 255}, "_width": 4, "_id": "4cKfWCVuFCeKTHgSfJKsdR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 360, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "bdYG2ZrwxLzqPUdwD8RAgf"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fabade8d-544f-4cd7-89cb-0fa3b65145dd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "ffReMgtTtO8Lxruf8fcBdw"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": null, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 100, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 4, "_id": "9erv1BGchFyrVu8ucp+0Tq"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "fabade8d-544f-4cd7-89cb-0fa3b65145dd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": "930/kptexOq7YsB2Us9Zuq"}, {"__type__": "961aeGllcFJcaHP0vKDnBY9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": null, "ctaNode": {"__id__": 212}, "scaleAmount": 1.1, "duration": 0.5, "useBackEasing": false, "_id": "fba1Kmff5CoIOB4IJs6v70"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "daGaYqTw5GDK4c6vN3JNWQ"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 86}, "_alignCanvasWithScreen": true, "_id": "c0cB2mXoRParsJ8ea1X7hX"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "1fko42YF9CBapZWlxUz7OK"}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "40d2db30-0c9f-4016-ba52-67756a575f97", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 121}]}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 230}, "shadows": {"__id__": 231}, "_skybox": {"__id__": 232}, "fog": {"__id__": 233}, "octree": {"__id__": 234}, "lightProbeInfo": {"__id__": 235}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 1, "y": 1, "z": 1, "w": 0.1}, "_skyIllumLDR": 0.1, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.618555, "y": 0.577848, "z": 0.544564, "w": 0}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 1024, "y": 1024}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": false, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null}]