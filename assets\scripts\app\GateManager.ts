import { _decorator, Component, Enum, Node, Prefab, instantiate } from 'cc';
import { GateDataConfig } from '../enums/LevelDataConfig';
import { GateType } from '../enums/Enums';
import { GateController } from '../game/controller/GateController';
const { ccclass, property } = _decorator;


@ccclass('GatePrefabMapping')
class GatePrefabMapping {
    @property({
        type: Enum(GateType),
        tooltip: "The shape of the element this configuration applies to."
    })
    gateType: GateType = GateType.Gate1;
    @property(Prefab)
    prefab: Prefab | null = null;
}

@ccclass('GateManager')
export class GateManager extends Component {

    @property([GatePrefabMapping])
    private gatePrefabs: GatePrefabMapping[] = [];

    public initialize(gateConfigs: GateDataConfig[]): void {

        for (const gateConfig of gateConfigs) {
            // console.log(gateConfig);
            const gatePrefab = this.gatePrefabs.find(gate => gate.gateType === gateConfig.Type)?.prefab;

            if (gatePrefab) {
                const gateNode = instantiate(gatePrefab);
                gateNode.setParent(this.node);

                const gateController = gateNode.getComponent(GateController);
                if (gateController) {
                    gateController.initialize(gateConfig);
                }
            }


        }
    }
}


