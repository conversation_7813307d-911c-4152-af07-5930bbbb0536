import { _decorator, Component, Node, tween, Vec3, CCInteger, ParticleSystem2D, CCBoolean } from 'cc';
import { UnlockCity } from './UnlockCity';
import { SoundManager } from '../app/SoundManager';
import { GameManager } from '../core/GameManager';
import { GameState } from '../enums/GameState';
import { GenerateMask } from '../ui/ChooseCity/GenerateMask';

const { ccclass, property } = _decorator;

@ccclass('ProgressUnlockCity')
export class ProgressUnlockCity extends Component {
    // --- PROPERTIES ---
    @property(CCInteger)
    private initProgress = 0;

    @property(ParticleSystem2D)
    private particle: ParticleSystem2D;

    @property(CCBoolean)
    private isOneClick: boolean = false;

    private generateMask: GenerateMask = null;

    public get GenerateMask(): GenerateMask {
        return this.generateMask;
    }

    // --- PRIVATE MEMBERS ---
    private _currentCityID = 0;
    private _currentCityProgress = 0;
    private _currentUnlockCity: UnlockCity = null;
    private _isEndGame = false;


    private static _instance: ProgressUnlockCity = null;

    public static get Instance(): ProgressUnlockCity {
        return this._instance;
    }


    // --- LIFECYCLE METHODS ---


    public initialize(cityID: number) {

        if (ProgressUnlockCity._instance === null) {
            ProgressUnlockCity._instance = this;
            this.generateMask = this.node.getComponent(GenerateMask);

            if (this.generateMask) {
                console.log('initialize');
                this.generateMask.generateMask();
            }

        } else {
            this.node.destroy();
        }

        this._currentCityID = cityID;

        if (this._currentCityID < 3) {
            this.showCity();
        }

        GameManager.Instance.onChangeState.on(this.onChangeState, this);
    }

    private onChangeState(state: GameState) {
        switch (state) {
            case GameState.Win:
                this.playAnimationEndGame(true);
                break;
            case GameState.Lose:
                this.playAnimationEndGame(false);
                break;
        }
    }

    // --- PUBLIC METHODS ---
    public setCity(cityID: number) {
        this._currentCityID = cityID;
        this.showCity();
    }

    public unlockCurrentCity(progress: number) {
        if (!this._currentUnlockCity) return;

        if (this.isOneClick) {

            GameManager.Instance.setGameState(GameState.Win);
            return;
        }

        const cityTotalFragments = this._currentUnlockCity.node.children.length;

        for (let i = 0; i < progress; i++) {
            const indexToUnlock = this._currentCityProgress;

            if (indexToUnlock >= cityTotalFragments) {
                break;
            }

            this._currentCityProgress++;

            if (this.generateMask) {
                this.generateMask.playReveal(this._currentUnlockCity.node, indexToUnlock, () => {
                    this.playAnimationUnlockCity(indexToUnlock);
                });
            } else {
                this.playAnimationUnlockCity(indexToUnlock);
            }
        }
    }

    private playAnimationUnlockCity(index: number) {
        this._currentUnlockCity.UnlockPerFragment(index);

        const fragmentNode = this._currentUnlockCity.node.children[index];
        if (fragmentNode) {
            this.particle.node.setWorldPosition(fragmentNode.worldPosition);
            this.particle.resetSystem();

            SoundManager.Instance.playSfx(SoundManager.Instance.Star_Appear);
        }

        let isCityCompleted = this._currentCityProgress >= this._currentUnlockCity.node.children.length;


        if (isCityCompleted) {
            this.handleCityCompleted();
        }
    }

    public getCurrentProgress(): number {
        return this._currentCityProgress;
    }

    public getCurrentCityID(): number {
        return this._currentCityID;
    }

    public playAnimationEndGame(isShow: boolean) {
        this._isEndGame = true;
        this.portraitLayout();

        const cityNode = this.activateCityNode(this._currentCityID);
        const unlockCity = cityNode.getComponent(UnlockCity);

        if (isShow) {
            unlockCity.UnlockAll();
        } else {
            unlockCity.LockAll();
        }

        this.animateEndGame(cityNode, isShow);
    }

    public portraitLayout() {
        this.node.setPosition(Vec3.ZERO);
        this.node.setScale(Vec3.ONE);
    }

    public landscapeLayout() {
        if (this._isEndGame) {
            this.portraitLayout();
        } else {
            this.node.setPosition(new Vec3(460, 328, 0));
            this.node.setScale(new Vec3(1.65, 1.65, 1.65));
        }
    }

    // --- PRIVATE METHODS ---
    private showCity() {
        this.activateCityNode(this._currentCityID);
        this._currentCityProgress = this.initProgress;
        // this._currentUnlockCity.UnlockPerFragment(this.initProgress);
    }

    private activateCityNode(cityID: number): Node {
        this.node.children.forEach((child, index) => {
            child.active = index === cityID;
        });
        const cityNode = this.node.children[cityID];
        if (cityNode) {
            this._currentUnlockCity = cityNode.getComponent(UnlockCity);
        }
        return cityNode;
    }

    private handleCityCompleted() {
        tween(this.node)
            .delay(0.4)
            .call(() => {
                // add vfx
                GameManager.Instance.setGameState(GameState.Win);
            })
            .start();
    }

    private animateEndGame(cityNode: Node, isShow: boolean) {
        tween(cityNode)
            .to(0.4, {
                position: new Vec3(0, 120, 0),
                scale: new Vec3(0.38, 0.38, 0.38)
            }, {
                easing: 'backOut'
            })
            .call(() => {
                if (isShow) {
                    this.startFloatingAnimation(cityNode);
                }
            })
            .start();
    }

    private startFloatingAnimation(node: Node) {
        const originalPos = node.position.clone();
        const floatUp = tween().to(1.5, { position: new Vec3(originalPos.x, originalPos.y + 10, originalPos.z) }, { easing: 'sineInOut' });
        const floatDown = tween().to(1.5, { position: originalPos }, { easing: 'sineInOut' });

        tween(node)
            .repeatForever(
                tween().sequence(floatUp, floatDown)
            )
            .start();
    }
}


