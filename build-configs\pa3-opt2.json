{"name": "pa3-opt2", "platform": "web-mobile", "buildPath": "project://build", "debug": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "polyfills": {"asyncFunctions": true, "coreJs": false}, "experimentalEraseModules": false, "useBuiltinServer": false, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": false, "packAutoAtlas": true, "startScene": "c6d5ab19-a26e-4241-9f49-2867a7041712", "outputName": "web-mobile", "scenes": [{"url": "db://assets/scenes/pa3-opt2.scene", "uuid": "c6d5ab19-a26e-4241-9f49-2867a7041712", "inBundle": false}, {"url": "db://assets/scenes/pa3-gameplay-opt2.scene", "uuid": "1b4e2383-594d-4504-a7bc-07c847519ccc", "inBundle": false}], "web-mobile": [], "bundleConfigs": [], "packages": {"web-mobile": {"orientation": "auto", "embedWebDebugger": false, "cullEngineAsmJsModule": true, "__version__": "1.0.1"}, "cocos-service": {"configID": "0e26b4", "services": [], "__version__": "3.0.7"}}, "__version__": "1.3.5"}