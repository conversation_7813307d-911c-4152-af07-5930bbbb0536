import { _decorator, Component, Node, Camera, view, Vec3, Label, Widget } from 'cc';
import { IAutoLayoutGameplay } from './AutoLayoutGameplay';
const { ccclass, property } = _decorator;

@ccclass('AutoLayoutGameplay3')
export class AutoLayoutGameplay3 extends Component implements IAutoLayoutGameplay {
    @property(Node)
    bg: Node = null!;
    @property(Camera)
    camera: Camera = null!;

    @property(Node)
    containLabel: Node = null!;

    @property(Node)
    tutorial: Node = null!;

    @property(Node)
    bottomUI: Node = null!;

    @property(Node)
    btn_CTA: Node = null!;

    @property(Node)
    ic_Left: Node = null!;


    private cam_pos_landscape: Vec3 = new Vec3(0, 38, -5.4);
    private cam_pos_portrait: Vec3 = new Vec3(0, 38, -7.4);

    private cam_rotate: Vec3 = new Vec3(-76, -180, 0);

    private cam_fov_landscape: number = 50;
    private cam_fov_portrait: number = 60;

    private label_scale_landscape: Vec3 = new Vec3(1.2, 1.2, 1.2);
    private label_scale_portrait: Vec3 = new Vec3(1, 1, 1);

    private tut_pos_landscape: Vec3 = new Vec3(-312, -278, 0);
    private tut_pos_portrait: Vec3 = new Vec3(0, 0, 0);

    private tut_scale_landscape: Vec3 = new Vec3(1.6, 1.6, 1.6);
    private tut_scale_portrait: Vec3 = new Vec3(1, 1, 1);

    private bottomUI_scale_landscape: Vec3 = new Vec3(1, 1, 1);
    private bottomUI_scale_portrait: Vec3 = new Vec3(0.6, 0.6, 0.6);

    onLoad() {
        this.updateLayout();
        view.setResizeCallback(this.updateLayout.bind(this));
    }

    onDestroy() {
        view.setResizeCallback(null);
    }

    private updateLayout() {
        const frameSize = view.getFrameSize();
        const isLandscape = frameSize.width > frameSize.height;
        if (!this.bg || !this.camera) return;

        this.bg.children[0].active = !isLandscape;
        this.bg.children[1].active = isLandscape;

        this.camera.node.setPosition(isLandscape ? this.cam_pos_landscape : this.cam_pos_portrait);
        // this.camera.node.setRotationFromEuler(this.cam_rotate);
        this.camera.fov = isLandscape ? this.cam_fov_landscape : this.cam_fov_portrait;

        this.containLabel.scale = isLandscape ? this.label_scale_landscape : this.label_scale_portrait;
        this.tutorial.setPosition(isLandscape ? this.tut_pos_landscape : this.tut_pos_portrait);
        this.tutorial.scale = isLandscape ? this.tut_scale_landscape : this.tut_scale_portrait;
        this.bottomUI.setScale(isLandscape ? this.bottomUI_scale_landscape : this.bottomUI_scale_portrait);

        this.btn_CTA.getComponent(Widget).bottom = 36;
        if (this.ic_Left) this.ic_Left.getComponent(Widget).bottom = 24;
        if (isLandscape) {
            if (this.ic_Left) this.ic_Left.getComponent(Widget).left = frameSize.width / 2.2;
            this.btn_CTA.getComponent(Widget).right = frameSize.width / 2.2;
        } else {
            if (this.ic_Left) this.ic_Left.getComponent(Widget).left = 48;
            this.btn_CTA.getComponent(Widget).right = 48;
        }
    }

    public setTutorialPosition(tut_pos_landscape: Vec3, tut_pos_portrait: Vec3) {
        this.tut_pos_landscape = tut_pos_landscape;
        this.tut_pos_portrait = tut_pos_portrait;
        this.updateLayout();
    }
}


