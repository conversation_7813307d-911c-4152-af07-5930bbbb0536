export enum ColorType {
    None = -1,
    <PERSON> = 11,
    <PERSON><PERSON><PERSON> = 1,
    <PERSON> = 2,
    <PERSON> = 3,
    <PERSON> = 4,
    <PERSON> = 5,
    <PERSON><PERSON><PERSON> = 6,
    <PERSON> = 7,
    <PERSON> = 8,
    <PERSON><PERSON> = 9,
    <PERSON><PERSON><PERSON> = 10,
}

export enum ElementShape {
    One = 1,
    Two = 2,
    Three = 3,
    <PERSON> = 4,
    <PERSON>erse<PERSON> = 5,
    <PERSON>L = 6,
    Plus = 7,
    Square = 8,
    ShortT = 9
}

export enum ObstacleType {
    Corner = 0,
    Side1 = 1,
    Side2 = 2,
    Side3 = 3,
}

export enum GateType {
    None = 0,
    Gate1 = 1,
    Gate2 = 2,
    Gate3 = 3,
}

export enum Direction {
    Horizontal = 0,
    Vertical = 1,
}
