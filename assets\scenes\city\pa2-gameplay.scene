[{"__type__": "cc.SceneAsset", "_name": "pa2-gameplay", "_objFlags": 0, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "pa2-gameplay", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 8}, {"__id__": 16}, {"__id__": 68}], "_active": true, "_components": [], "_prefab": {"__id__": 239}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 240}, "_id": "b6f1529f-418e-42de-a561-84f8c28ff6a0"}, {"__type__": "cc.Node", "_name": "Enviroment", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 6}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "02BvN2/MZMxqfau/UlU/t5"}, {"__type__": "cc.Node", "_name": "Main Light", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": -0.8660254037844386, "y": 0, "z": 0, "w": 0.5000000000000001}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 0.9999999999999999, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": -120, "y": 0, "z": 0}, "_id": "c0y6F5f+pAvI805TdmxIjx"}, {"__type__": "cc.DirectionalLight", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useColorTemperature": false, "_colorTemperature": 8500, "_staticSettings": {"__id__": 5}, "_visibility": -325058561, "_illuminanceHDR": 65000, "_illuminance": 65000, "_illuminanceLDR": 4, "_shadowEnabled": false, "_shadowPcf": 0, "_shadowBias": 1e-05, "_shadowNormalBias": 0, "_shadowSaturation": 1, "_shadowDistance": 50, "_shadowInvisibleOcclusionRange": 200, "_csmLevel": 4, "_csmLayerLambda": 0.75, "_csmOptimizationMode": 2, "_csmAdvancedOptions": false, "_csmLayersTransition": false, "_csmTransitionRange": 0.05, "_shadowFixedArea": false, "_shadowNear": 0.1, "_shadowFar": 10, "_shadowOrthoSize": 5, "_id": "597uMYCbhEtJQc0ffJlcgA"}, {"__type__": "cc.StaticLightSettings", "_baked": false, "_editorOnly": false, "_castShadow": false}, {"__type__": "cc.Node", "_name": "Main Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 7}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -10, "y": 10, "z": 10}, "_lrot": {"__type__": "cc.Quat", "x": -0.2778159334694406, "y": -0.3649716762170988, "z": -0.1150751274863838, "w": 0.8811195706053617}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9999999999999999, "y": 0.9999999999999999, "z": 0.9999999999999999}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": -35.000000000000014, "y": -45.00000000000001, "z": 0}, "_id": "c9DMICJLFO5IeO07EPon7U"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 6}, "_enabled": true, "__prefab": null, "_projection": 1, "_priority": 0, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 10, "_near": 1, "_far": 1000, "_color": {"__type__": "cc.Color", "r": 51, "g": 51, "b": 51, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 14, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1822425087, "_targetTexture": null, "_cameraType": -1, "_trackingType": 0, "_id": "7dWQTpwS5LrIHnc1zAPUtf"}, {"__type__": "cc.Node", "_name": "GameSystem", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 9}, {"__id__": 12}, {"__id__": 14}, {"__id__": 237}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "cd7QFsQm9Kx6VRn0Lv11q7"}, {"__type__": "cc.Node", "_name": "GameFlow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 8}, "_children": [], "_active": true, "_components": [{"__id__": 10}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "bf/OaOZzJP14eP2zDtdm3L"}, {"__type__": "67090FWWv9IBaK0NzXmcxUG", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": null, "gameManager": {"__id__": 11}, "_id": "e1u13cNGlIL7uHHMkGz5CX"}, {"__type__": "552b9xV2X1J5YtPvwq1B33I", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": null, "soundManager": {"__id__": 13}, "gameplay": {"__id__": 15}, "inputManager": {"__id__": 18}, "_id": "95VTy6cwdPMKKgfYZ60i2g"}, {"__type__": "cc.Node", "_name": "GameManager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 8}, "_children": [], "_active": true, "_components": [{"__id__": 11}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0bguVXkBxCKq82EINnonkz"}, {"__type__": "b6c059j/LBMRZ+Vt2xEQBus", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": null, "BGM": {"__uuid__": "06d05b0f-8873-4439-842d-9f6acd5549d8", "__expectedType__": "cc.AudioClip"}, "Button_Click": {"__uuid__": "961c172f-47cc-4363-9daa-b84d9054bf39", "__expectedType__": "cc.AudioClip"}, "Clear_Block": {"__uuid__": "c36f9a56-34b5-47a0-887d-e02f73782cd8", "__expectedType__": "cc.AudioClip"}, "Game_Over": {"__uuid__": "43faba26-f489-4ba5-98bc-003c579a87f7", "__expectedType__": "cc.AudioClip"}, "Win": {"__uuid__": "053fb1fb-c6f5-44ee-906c-0ae5c3c7ceac", "__expectedType__": "cc.AudioClip"}, "Level_Complete": {"__uuid__": "7a7bf3de-dd86-433f-b943-b53fd5d23445", "__expectedType__": "cc.AudioClip"}, "Deselect_Block": {"__uuid__": "bbeeb7ac-d14b-4879-979c-baa358028290", "__expectedType__": "cc.AudioClip"}, "Select_Block": {"__uuid__": "a52fa2cd-724b-473b-b8b3-67970c4a8258", "__expectedType__": "cc.AudioClip"}, "Time_Out": {"__uuid__": "2bf34d59-0af8-4c13-b4e5-f68da33de521", "__expectedType__": "cc.AudioClip"}, "Star_Appear": {"__uuid__": "71b6e6a6-c2c6-475e-9404-f03645<PERSON>beed", "__expectedType__": "cc.AudioClip"}, "_id": "bcG03Vn+pIYJxamD2C7lny"}, {"__type__": "cc.Node", "_name": "SoundManager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 8}, "_children": [], "_active": true, "_components": [{"__id__": 13}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "73xTxpp5ZEs4k5Z2YR2504"}, {"__type__": "bc7bd7iQglAAplcgWgk39ym", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 16}, "_enabled": true, "__prefab": null, "boardController": {"__id__": 41}, "levelManager": {"__id__": 46}, "unlockCity": {"__id__": 66}, "uiCanvas": {"__id__": 160}, "_id": "09GdayMJZKVqUe29qdXC5O"}, {"__type__": "cc.Node", "_name": "Gameplay", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 17}, {"__id__": 45}, {"__id__": 20}, {"__id__": 47}, {"__id__": 59}], "_active": true, "_components": [{"__id__": 15}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b3wmtU8xFOvJuOtalHmTb2"}, {"__type__": "cc.Node", "_name": "InputManager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 16}, "_children": [], "_active": true, "_components": [{"__id__": 18}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b1ZTXj1HFOgZfHO2h3B+VJ"}, {"__type__": "1cb14I7bhVB847R4ovHbCQh", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 17}, "_enabled": true, "__prefab": null, "plane": {"__id__": 19}, "camera": {"__id__": 7}, "dragSpeed": 50, "maxVelocity": 50, "smoothFactor": 0.8, "dampingFactor": 0.95, "_id": "3egVQGhL5Kzq1Lz5naM/WZ"}, {"__type__": "cc.Node", "_name": "Plane", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 42}, {"__id__": 44}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 1.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 10, "y": 1, "z": 10}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "02WDi7h8FNVoRMe7dyWhDX"}, {"__type__": "cc.Node", "_name": "BoardController", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 16}, "_children": [{"__id__": 19}, {"__id__": 21}, {"__id__": 23}, {"__id__": 29}, {"__id__": 34}], "_active": true, "_components": [{"__id__": 41}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "97w79RpXND8Z3vSUZIriqn"}, {"__type__": "cc.Node", "_name": "Floors", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 22}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "27NzMB4XJHuIhTjlSxtDS3"}, {"__type__": "7f4fbtqlrNErK1rZP7MyPMg", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": null, "cellPrefab": {"__uuid__": "28e2702d-c052-4306-be8e-0403194ba88c", "__expectedType__": "cc.Prefab"}, "offsetZ": 0.5, "_id": "fdONqLtrFHVbf26egxqHpT"}, {"__type__": "cc.Node", "_name": "Obstacles", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 24}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ab2wTnIztKGq/1JoXku78y"}, {"__type__": "945e6+P2cdDa5mgNzUUsKtD", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 23}, "_enabled": true, "__prefab": null, "obstaclePrefabs": [{"__id__": 25}, {"__id__": 26}, {"__id__": 27}, {"__id__": 28}], "_id": "f0UQ3KltJOBZ3aw7lJyBpZ"}, {"__type__": "ObstaclePrefabItem", "type": 0, "prefab": {"__uuid__": "8ecd179b-40e6-4003-a190-cdaa47c99326", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObstaclePrefabItem", "type": 1, "prefab": {"__uuid__": "9ca8097d-9180-492b-98b8-1cf9c2150f36", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObstaclePrefabItem", "type": 2, "prefab": {"__uuid__": "97abf1e8-ca46-42b7-81d3-226667b9cbfe", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObstaclePrefabItem", "type": 3, "prefab": {"__uuid__": "8a6a2d09-720a-4b5d-bb0d-af68fa557f76", "__expectedType__": "cc.Prefab"}}, {"__type__": "cc.Node", "_name": "Gates", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 30}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5fDNFxbWtDpozpkm+6oEQ1"}, {"__type__": "f48f7JNskxCj4xFXsZywuiw", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": null, "gatePrefabs": [{"__id__": 31}, {"__id__": 32}, {"__id__": 33}], "_id": "cbr5+gS5dK9KKOPoxbi9vy"}, {"__type__": "GatePrefabMapping", "gateType": 1, "prefab": {"__uuid__": "d848b069-2aa6-428f-8af3-2593c32d7f85", "__expectedType__": "cc.Prefab"}}, {"__type__": "GatePrefabMapping", "gateType": 2, "prefab": {"__uuid__": "76b7b698-5492-46b3-962d-f094883d0d57", "__expectedType__": "cc.Prefab"}}, {"__type__": "GatePrefabMapping", "gateType": 3, "prefab": {"__uuid__": "07addf65-b572-4f31-a42d-6ca954952bc0", "__expectedType__": "cc.Prefab"}}, {"__type__": "cc.Node", "_name": "Elements", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 35}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "fdfAiO5NFJlplCKF0hsFIL"}, {"__type__": "2ef56yheppPoLUtrzohXwVZ", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": null, "elementPrefabs": [{"__id__": 36}, {"__id__": 37}, {"__id__": 38}, {"__id__": 39}, {"__id__": 40}], "offsetZ": 1, "_id": "6fRPmOL9pOZZLy/YxLZV4d"}, {"__type__": "ElementPrefabMapping", "shapeName": 1, "prefab": {"__uuid__": "8e789533-0ac5-45f3-aaf1-579ce261bbfc", "__expectedType__": "cc.Prefab"}}, {"__type__": "ElementPrefabMapping", "shapeName": 2, "prefab": {"__uuid__": "7c7ee679-3c2a-4185-8a73-36d438b2142d", "__expectedType__": "cc.Prefab"}}, {"__type__": "ElementPrefabMapping", "shapeName": 3, "prefab": {"__uuid__": "fef66d29-5a07-4f40-9d52-3f3528b7201e", "__expectedType__": "cc.Prefab"}}, {"__type__": "ElementPrefabMapping", "shapeName": 6, "prefab": {"__uuid__": "9f94c442-7504-4669-af50-7fd7a4e276fe", "__expectedType__": "cc.Prefab"}}, {"__type__": "ElementPrefabMapping", "shapeName": 8, "prefab": {"__uuid__": "f5b6bb7f-07c3-432d-b403-b7cb369eb090", "__expectedType__": "cc.Prefab"}}, {"__type__": "c9fbcTgZv5Pv43kazZvee+h", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "floorView": {"__id__": 22}, "obstacleManager": {"__id__": 24}, "gateManager": {"__id__": 30}, "blockManagers": {"__id__": 35}, "_id": "51Kqp4+GNFu6UsVD08/3Zv"}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": false, "__prefab": null, "_materials": [], "_visFlags": 0, "bakeSettings": {"__id__": 43}, "_mesh": {"__uuid__": "1263d74c-8167-4928-91a6-4e2672411f47@2e76e", "__expectedType__": "cc.Mesh"}, "_shadowCastingMode": 0, "_shadowReceivingMode": 1, "_shadowBias": 0, "_shadowNormalBias": 0, "_reflectionProbeId": -1, "_enableMorph": true, "_id": "eeh9Jo9xFCz6oRqI9DJfBX"}, {"__type__": "cc.ModelBakeSettings", "texture": null, "uvParam": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_bakeable": false, "_castShadow": false, "_receiveShadow": false, "_recieveShadow": false, "_lightmapSize": 64, "_useLightProbe": false, "_bakeToLightProbe": true, "_reflectionProbeType": 0, "_bakeToReflectionProbe": true, "_probeCubemap": null}, {"__type__": "<PERSON>.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": null, "_material": null, "_isTrigger": true, "_center": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_mesh": {"__uuid__": "1263d74c-8167-4928-91a6-4e2672411f47@2e76e", "__expectedType__": "cc.Mesh"}, "_convex": false, "_id": "b5XLoac4ZGs6UKc5Wj9tEn"}, {"__type__": "cc.Node", "_name": "LevelManager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 16}, "_children": [], "_active": true, "_components": [{"__id__": 46}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8eY8814HFEBq2zkQxY4A+j"}, {"__type__": "d0506x5PIJMPJWOoEd/FVRO", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 45}, "_enabled": true, "__prefab": null, "jsonData": {"__uuid__": "db45f6f9-71ab-416a-a3de-4d2c957d60fb", "__expectedType__": "cc.Json<PERSON>set"}, "_id": "0e7CHSPtdOtKQeZf1Qtxui"}, {"__type__": "cc.Node", "_name": "ColorConfigs", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 16}, "_children": [], "_active": true, "_components": [{"__id__": 48}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4cP9MzwgdDsIPvXnrNFBDX"}, {"__type__": "91ef5kEJkNAo4LG6h86zRND", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 47}, "_enabled": true, "__prefab": null, "colorMappings": [{"__id__": 49}, {"__id__": 50}, {"__id__": 51}, {"__id__": 52}, {"__id__": 53}, {"__id__": 54}, {"__id__": 55}, {"__id__": 56}, {"__id__": 57}, {"__id__": 58}], "outlineMaterial": {"__uuid__": "b61f823b-4514-47db-a023-4fdc70f836cd", "__expectedType__": "cc.Material"}, "originalMaterial": {"__uuid__": "fe48ed51-f544-4266-989b-48087860ed89", "__expectedType__": "cc.Material"}, "_id": "ebRd79MgVH35czy5zYdSaL"}, {"__type__": "ColorMapping", "colorType": 11, "texture": {"__uuid__": "fdb9a4e8-1809-4ed9-8ba7-1ebcf6b71fff@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "ColorMapping", "colorType": 1, "texture": {"__uuid__": "1cec19fb-3b48-4008-ac77-f82721f62d19@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "ColorMapping", "colorType": 2, "texture": {"__uuid__": "5f075cd5-462f-4b0b-aa3f-9447a9965ada@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "ColorMapping", "colorType": 3, "texture": {"__uuid__": "f5efd4dc-f19e-40d5-905e-b046bdd86b8a@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "ColorMapping", "colorType": 4, "texture": {"__uuid__": "82ba8c3f-ad50-4031-b2fe-47ae3f3890a3@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "ColorMapping", "colorType": 5, "texture": {"__uuid__": "3033b392-8314-4ef1-813b-7f75b2e8a562@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "ColorMapping", "colorType": 6, "texture": {"__uuid__": "7f69600c-0fff-452c-9cbc-87c35d1ac904@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "ColorMapping", "colorType": 7, "texture": {"__uuid__": "4d026fae-9902-464f-ab33-bbefbdc56c68@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "ColorMapping", "colorType": 8, "texture": {"__uuid__": "953e1886-7d40-4832-86cc-c1accbceaf75@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "ColorMapping", "colorType": 9, "texture": {"__uuid__": "2e52ba89-42a6-4c72-9ad8-d6ebfc40d902@6c48a", "__expectedType__": "cc.Texture2D"}}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 16}, "_children": [{"__id__": 60}, {"__id__": 63}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "13dPUbbQlHK4Jt8jWl0ItD"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 59}, "_children": [], "_active": true, "_components": [{"__id__": 61}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -1, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": -3.204653064661855e-18, "y": 0.9986295347545739, "z": -0.05233595624294384, "w": -6.114842316355998e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 4.5, "y": 0.9999999999999999, "z": 6.5}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 6.000000000000002, "y": -180, "z": 8.827812596100317e-32}, "_id": "bfL6IwtXFNjq/s4a2y4I6d"}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "Plane<ModelComponent>", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": null, "_materials": [{"__uuid__": "10825948-ef96-4d95-9a66-e594e880423d", "__expectedType__": "cc.Material"}], "_visFlags": 0, "bakeSettings": {"__id__": 62}, "_mesh": {"__uuid__": "1263d74c-8167-4928-91a6-4e2672411f47@2e76e", "__expectedType__": "cc.Mesh"}, "_shadowCastingMode": 0, "_shadowReceivingMode": 1, "_shadowBias": 0, "_shadowNormalBias": 0, "_reflectionProbeId": -1, "_enableMorph": true, "_id": "efP568Xz9MIYimEVt8t55h"}, {"__type__": "cc.ModelBakeSettings", "texture": null, "uvParam": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_bakeable": false, "_castShadow": false, "_receiveShadow": false, "_recieveShadow": false, "_lightmapSize": 64, "_useLightProbe": false, "_bakeToLightProbe": true, "_reflectionProbeType": 0, "_bakeToReflectionProbe": true, "_probeCubemap": null}, {"__type__": "cc.Node", "_name": "bg-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 59}, "_children": [], "_active": false, "_components": [{"__id__": 64}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -8, "y": -1, "z": 8}, "_lrot": {"__type__": "cc.Quat", "x": -3.2046530646618555e-18, "y": 0.9986295347545739, "z": -0.05233595624294384, "w": -6.114842316355998e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 6.5, "y": 0.9999999999999997, "z": 6.5}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 6.000000000000002, "y": -180, "z": 4.4139062980501586e-32}, "_id": "21Q8cF5WRHpYyYie1h4Y3U"}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "Plane<ModelComponent>", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 63}, "_enabled": true, "__prefab": null, "_materials": [{"__uuid__": "10825948-ef96-4d95-9a66-e594e880423d", "__expectedType__": "cc.Material"}], "_visFlags": 0, "bakeSettings": {"__id__": 65}, "_mesh": {"__uuid__": "1263d74c-8167-4928-91a6-4e2672411f47@2e76e", "__expectedType__": "cc.Mesh"}, "_shadowCastingMode": 0, "_shadowReceivingMode": 1, "_shadowBias": 0, "_shadowNormalBias": 0, "_reflectionProbeId": -1, "_enableMorph": true, "_id": "fdBDHLNxBPT71x6BQgphuP"}, {"__type__": "cc.ModelBakeSettings", "texture": null, "uvParam": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_bakeable": false, "_castShadow": false, "_receiveShadow": false, "_recieveShadow": false, "_lightmapSize": 64, "_useLightProbe": false, "_bakeToLightProbe": true, "_reflectionProbeType": 0, "_bakeToReflectionProbe": true, "_probeCubemap": null}, {"__type__": "cc.Node", "_name": "ProgressUnlock", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 67}, "_children": [{"__id__": 164}, {"__id__": 191}, {"__id__": 212}], "_active": true, "_components": [{"__id__": 236}, {"__id__": 162}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "adk9euJ2BHc7LgEQ1KEhg9"}, {"__type__": "cc.Node", "_name": "containCity", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 68}, "_children": [{"__id__": 66}], "_active": true, "_components": [{"__id__": 163}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -40, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "954BmbfSpAlq7pI8YxCgZw"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 69}, {"__id__": 71}, {"__id__": 93}, {"__id__": 112}, {"__id__": 116}, {"__id__": 123}, {"__id__": 67}, {"__id__": 141}, {"__id__": 144}], "_active": true, "_components": [{"__id__": 157}, {"__id__": 158}, {"__id__": 159}, {"__id__": 160}, {"__id__": 161}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 320, "y": 480, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "67Jrl0gOdCSqgsjljKkXLH"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 68}, "_children": [], "_active": true, "_components": [{"__id__": 70}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e3CakCw/hAAKI6li7Mgznx"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 69}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 1073741824, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 480, "_near": 1, "_far": 2000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 6, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 41943040, "_targetTexture": null, "_cameraType": -1, "_trackingType": 0, "_id": "1blg0fpZpKAos+XV3y2v9W"}, {"__type__": "cc.Node", "_name": "EndScreen", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 68}, "_children": [{"__id__": 72}, {"__id__": 79}, {"__id__": 82}, {"__id__": 85}], "_active": false, "_components": [{"__id__": 90}, {"__id__": 91}, {"__id__": 92}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "139RZ0kMxKQJQsONM2h48A"}, {"__type__": "cc.Node", "_name": "Btn_Play", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 71}, "_children": [], "_active": true, "_components": [{"__id__": 73}, {"__id__": 74}, {"__id__": 75}, {"__id__": 76}, {"__id__": 77}, {"__id__": 78}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -313.99999999999994, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d1XHckijFIMoKJrOqyVoG3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 214, "height": 86}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "88AYErEcpN8LxDC6oXPOZ3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fabade8d-544f-4cd7-89cb-0fa3b65145dd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "2cQah3HtZGpYCDy2dSTP00"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": null, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 123, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "a4nqeOOw5FRYgeCi3BEWKZ"}, {"__type__": "8c2e3Bq3+tHbIhK6acZcV17", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": null, "_id": "b3zkYaKEJCRozLM4mRA6sg"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": "17TbANPW1OLYlTU8MXL5Kb"}, {"__type__": "961aeGllcFJcaHP0vKDnBY9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": null, "ctaNode": {"__id__": 72}, "scaleAmount": 1.1, "duration": 0.5, "useBackEasing": true, "_id": "d11+kU885Awq3MhF4mS0YD"}, {"__type__": "cc.Node", "_name": "particle1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 71}, "_children": [], "_active": true, "_components": [{"__id__": 80}, {"__id__": 81}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e2TxxTglpBCLLkPtkmKCab"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 79}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "dax0/4rCtDz56q8ZsCePLS"}, {"__type__": "cc.ParticleSystem2D", "_name": "Node<ParticleSystem2D>", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 79}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 1, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "duration": 2, "emissionRate": 5, "life": 0.8, "lifeVar": 0.3, "angle": 360, "angleVar": 360, "startSize": 50, "startSizeVar": 36, "endSize": 30, "endSizeVar": 30, "startSpin": 25, "startSpinVar": 25, "endSpin": 40, "endSpinVar": 40, "sourcePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "posVar": {"__type__": "cc.Vec2", "x": 150, "y": 150}, "emitterMode": 0, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "speed": 0, "speedVar": 0, "tangentialAccel": 0, "tangentialAccelVar": 0, "radialAccel": 0, "radialAccelVar": 0, "rotationIsDir": false, "startRadius": 0, "startRadiusVar": 0, "endRadius": 0, "endRadiusVar": 0, "rotatePerS": 0, "rotatePerSVar": 0, "playOnLoad": false, "autoRemoveOnFinish": false, "_preview": true, "preview": true, "_custom": true, "_file": null, "_spriteFrame": {"__uuid__": "02d7e248-99ed-4449-afb9-585f3e5d2842@f9941", "__expectedType__": "cc.SpriteFrame"}, "_totalParticles": 10, "_startColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_startColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "_endColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_endColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "_positionType": 1, "_id": "8168HjBB9HS6DF/N2HWwwy"}, {"__type__": "cc.Node", "_name": "particle2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 71}, "_children": [], "_active": true, "_components": [{"__id__": 83}, {"__id__": 84}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "50PuPeg4NFIYBp9X7a3r5O"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d6ZliFjIFH/YAD8unb08hp"}, {"__type__": "cc.ParticleSystem2D", "_name": "Node<ParticleSystem2D>", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 1, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "duration": 0.02, "emissionRate": 1000, "life": 1, "lifeVar": 0.5, "angle": 360, "angleVar": 360, "startSize": 100, "startSizeVar": 50, "endSize": 30, "endSizeVar": 30, "startSpin": 0, "startSpinVar": 0, "endSpin": 0, "endSpinVar": 0, "sourcePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "posVar": {"__type__": "cc.Vec2", "x": 250, "y": 0}, "emitterMode": 0, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "speed": 100, "speedVar": 0, "tangentialAccel": 0, "tangentialAccelVar": 0, "radialAccel": 0, "radialAccelVar": 0, "rotationIsDir": false, "startRadius": 0, "startRadiusVar": 0, "endRadius": 0, "endRadiusVar": 0, "rotatePerS": 0, "rotatePerSVar": 0, "playOnLoad": false, "autoRemoveOnFinish": false, "_preview": true, "preview": true, "_custom": true, "_file": null, "_spriteFrame": {"__uuid__": "cb00d304-0d2c-4a1a-ad42-bd8dbadcea0d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_totalParticles": 1000, "_startColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_startColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "_endColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_endColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "_positionType": 1, "_id": "ecUsQLMShJVbz9pkLu53WN"}, {"__type__": "cc.Node", "_name": "containLogo", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 71}, "_children": [{"__id__": 86}], "_active": true, "_components": [{"__id__": 89}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 306.247, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9bEj9Y+khGSa5PxstcP5Tj"}, {"__type__": "cc.Node", "_name": "logo", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 85}, "_children": [], "_active": true, "_components": [{"__id__": 87}, {"__id__": 88}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 0.8}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4frV5Ek7VMjJU7djlqnXVh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 86}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 368, "height": 277}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ddYKLo5IdGG55jMr0wtx/g"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 86}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3dfe758d-4f19-46ad-b623-2d601ebc08e2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "edW+6JwNdCSri76ehGa/0e"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 85}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "95dLjs4ElIVqWOaDCtIFiN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 71}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 959.9999999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a0ZPl25HpEZrgPFV+cdTAo"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 71}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "da41yjky9BvIX5f1FC2Sh5"}, {"__type__": "ce5b10zfJFLMYmiRK0BrS19", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 71}, "_enabled": true, "__prefab": null, "logo": {"__id__": 85}, "particle": {"__id__": 81}, "particle2": {"__id__": 84}, "_id": "79C/keC5lBfrDmTd0Ps05P"}, {"__type__": "cc.Node", "_name": "TopUI", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 68}, "_children": [{"__id__": 94}], "_active": true, "_components": [{"__id__": 110}, {"__id__": 111}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a3qRz1uJFAObXTDHiFiIUs"}, {"__type__": "cc.Node", "_name": "Contain_Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 93}, "_children": [{"__id__": 95}, {"__id__": 99}], "_active": true, "_components": [{"__id__": 107}, {"__id__": 108}, {"__id__": 109}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 404, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "31YnjBan1Iyo2uY5BssdlU"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 94}, "_children": [], "_active": true, "_components": [{"__id__": 96}, {"__id__": 97}, {"__id__": 98}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -78.458, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "da6LD4i1RBVJvPHaa7HvJ7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 379.51, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "71hEcnLfxOf70IpDSNPm9y"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Grind blocks to build your city", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "0d1cc6ff-7de5-434d-9351-3d548f8e5045", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "25uIJ5B4JGMKnleoKF7glq"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 95}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_width": 2, "_id": "2f7b8w9YdDuIVTBfjzJ0Gw"}, {"__type__": "cc.Node", "_name": "ContainText", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 94}, "_children": [{"__id__": 100}, {"__id__": 103}], "_active": true, "_components": [{"__id__": 106}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 0.5}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0dmJaY3zxOW4Tuf9mBbkZc"}, {"__type__": "cc.Node", "_name": "Label-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 99}, "_children": [], "_active": true, "_components": [{"__id__": 101}, {"__id__": 102}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 51.454, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9bcMwvN9FKRbITFt7iXbrI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 100}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 105.65, "height": 70.56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "46wegRZcdAJZb3qjlcZSUX"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 100}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "TIME", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 38, "_fontSize": 38, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 56, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "0d1cc6ff-7de5-434d-9351-3d548f8e5045", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 1, "_id": "bbLU3MFuFA3qDaQt7/qybX"}, {"__type__": "cc.Node", "_name": "lblTime", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 99}, "_children": [], "_active": true, "_components": [{"__id__": 104}, {"__id__": 105}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -92.542, "y": 39.17, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "25evr/vbRCvbXDSOiFpr2o"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 103}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": "0eFHGh+sZP1pUEnBu5jQQM"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 103}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "00:00", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 64, "_fontSize": 64, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 120, "_overflow": 1, "_enableWrapText": false, "_font": {"__uuid__": "0d1cc6ff-7de5-434d-9351-3d548f8e5045", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 2, "_id": "7coFB4PQRCjZHmKaoT5ran"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 99}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "3d3xfsmdVPiLIY/fqDW1fY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 94}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 164, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4ae0LcAflB65GDcsdlOLg6"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 94}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "80ed134f-59de-403b-a5ca-2e9eece27be1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "aeHJ6OdoZGTZGtzSv+wxrm"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 94}, "_enabled": true, "__prefab": null, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 36, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 1, "_id": "a2Oh3vkHNA0KpYYXkQCVRR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 960}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "51f+eFqGdEyrdunttA70sm"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 45, "_id": "a6sPXjSzREGJZKTXZHwGI6"}, {"__type__": "cc.Node", "_name": "timeOutFx", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 68}, "_children": [], "_active": false, "_components": [{"__id__": 113}, {"__id__": 114}, {"__id__": 115}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b9eHYI5mxI3oJFaXi5aPOc"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 112}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 960}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "98DBdItHBMLoqKZJraKYRu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 112}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c829c9bd-321e-492a-aaef-a2c0b6b26da1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "3cXHnsowpLG5oxyLes2eD4"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 112}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_alignMode": 2, "_lockFlags": 0, "_id": "d4dhUtdrtNd5qrx6D78rMe"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 68}, "_children": [{"__id__": 117}], "_active": false, "_components": [{"__id__": 120}, {"__id__": 121}, {"__id__": 122}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -435.99999999999994, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "928klxDmZE0K/YuzB8oV/c"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 116}, "_children": [], "_active": false, "_components": [{"__id__": 118}, {"__id__": 119}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "10OK65Q0ZI2ph4Ez7do8TA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 117}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 500, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d70ueWLbJNwLhc8KAlCbPU"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 117}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Choose a city", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 42, "_fontSize": 41.9, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "d10be190-c4b3-4ce6-8a85-4fe22bdeb875", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "80IN1KJlhOgZJ34jttJrIO"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 116}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 664, "height": 116}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f3VD44usNJc5CKm+NRLJuW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 116}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d29c0512-04f1-449f-8c7f-0487621aef02@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "bbA1c1zEVIhLAcHhgpmaqu"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 116}, "_enabled": true, "__prefab": null, "_alignFlags": 44, "_target": null, "_left": -12, "_right": -12, "_top": 365.49999999999994, "_bottom": -14, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1088, "_originalHeight": 229, "_alignMode": 2, "_lockFlags": 0, "_id": "33osHlx2FHZaWH5w/UcVdY"}, {"__type__": "cc.Node", "_name": "tutorial", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 68}, "_children": [{"__id__": 124}], "_active": true, "_components": [{"__id__": 139}, {"__id__": 140}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -312, "y": -278, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.6, "y": 1.6, "z": 1.6}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5eLW/jvA5BJrwWeZ3D5ZNw"}, {"__type__": "cc.Node", "_name": "hand", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 123}, "_children": [{"__id__": 125}, {"__id__": 129}, {"__id__": 133}], "_active": true, "_components": [{"__id__": 136}, {"__id__": 137}, {"__id__": 138}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 50.893, "y": -21.926, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.3, "y": 0.3, "z": 0.3}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "53poLQMi5H0Z38Xch+7RY+"}, {"__type__": "cc.Node", "_name": "ring1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 124}, "_children": [], "_active": true, "_components": [{"__id__": 126}, {"__id__": 127}, {"__id__": 128}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "68vgAFwDRFi6BZ6zzOjqIS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 125}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 164, "height": 164}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "550EJrn7VEhJONZJYsqmRd"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 125}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6c3b39d4-d574-436a-8ae5-c6d955925a92@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "5cMPncEeVHa6sra/+tIHQE"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 125}, "_enabled": true, "__prefab": null, "_opacity": 0, "_id": "ddShCx1VVMI6THaCGHwg7L"}, {"__type__": "cc.Node", "_name": "ring2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 124}, "_children": [], "_active": true, "_components": [{"__id__": 130}, {"__id__": 131}, {"__id__": 132}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5aHWS4nBNDNpFa0HV4ZomP"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 129}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 164, "height": 164}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b8+iChQxBLSai/Z8pANbx0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 129}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6c3b39d4-d574-436a-8ae5-c6d955925a92@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "6a2u2BEqhNx5MBskyHIWp+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 129}, "_enabled": true, "__prefab": null, "_opacity": 0, "_id": "bceuJPpo9Ev5DMLXRQLkA0"}, {"__type__": "cc.Node", "_name": "Hand", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 124}, "_children": [], "_active": true, "_components": [{"__id__": 134}, {"__id__": 135}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.21643961393810288, "w": 0.9762960071199334}, "_lscale": {"__type__": "cc.Vec3", "x": 0.45, "y": 0.45, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 25}, "_id": "04fF5TdetMMIR1yNEVxCjX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 133}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 323, "height": 477}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.25, "y": 0.85}, "_id": "72bvOMWuZBdrdj1UqiKu7z"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 133}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fa7bd01b-4f00-43d4-9fdb-0d3edfab4377@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "a0WHMEW2pMLrPQ1GVWaqJC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b16gLHAYBOCKhBW1P9P3en"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": null, "playOnLoad": false, "_clips": [{"__uuid__": "0ddbd80e-5b13-41ac-b286-d7721e35cf57", "__expectedType__": "cc.AnimationClip"}], "_defaultClip": {"__uuid__": "0ddbd80e-5b13-41ac-b286-d7721e35cf57", "__expectedType__": "cc.AnimationClip"}, "_id": "f3OqD9MSdAwKsp8C4BQg9C"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 124}, "_enabled": true, "__prefab": null, "_opacity": 255, "_id": "75GGgKJB5CFZNoTaQ66+k6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "072+X2geRF7LocicElNxP3"}, {"__type__": "2fd33JTrEVH1Jo1u01tgb7J", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": null, "animation": {"__id__": 137}, "fadeInDuration": 0.3, "moveDuration": 0.4, "fadeOutDuration": 0.3, "loopDelay": 0.5, "_id": "590G5YaVFFaZWNOk98Xrsk"}, {"__type__": "cc.Node", "_name": "particle3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 68}, "_children": [], "_active": true, "_components": [{"__id__": 142}, {"__id__": 143}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -100.608, "y": -187.75, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1eYgpvtXpAEIA5+8+0s059"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 141}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "50yNc48glDmp1e1/F1FLRz"}, {"__type__": "cc.ParticleSystem2D", "_name": "Node<ParticleSystem2D>", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 141}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 1, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "duration": 1, "emissionRate": 8, "life": 0.8, "lifeVar": 0.3, "angle": 360, "angleVar": 360, "startSize": 10, "startSizeVar": 20, "endSize": 30, "endSizeVar": 40, "startSpin": 25, "startSpinVar": 25, "endSpin": 40, "endSpinVar": 40, "sourcePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "posVar": {"__type__": "cc.Vec2", "x": 50, "y": 50}, "emitterMode": 0, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "speed": 0, "speedVar": 0, "tangentialAccel": 0, "tangentialAccelVar": 0, "radialAccel": 0, "radialAccelVar": 0, "rotationIsDir": false, "startRadius": 0, "startRadiusVar": 0, "endRadius": 0, "endRadiusVar": 0, "rotatePerS": 0, "rotatePerSVar": 0, "playOnLoad": false, "autoRemoveOnFinish": false, "_preview": true, "preview": true, "_custom": true, "_file": null, "_spriteFrame": {"__uuid__": "7220256f-b356-4714-a278-e949697e61c5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_totalParticles": 10, "_startColor": {"__type__": "cc.Color", "r": 0, "g": 173, "b": 255, "a": 255}, "_startColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "_endColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_endColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "_positionType": 1, "_id": "87Y9VMPG5Ee6+DPemJjdUR"}, {"__type__": "cc.Node", "_name": "BottomUI", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 68}, "_children": [{"__id__": 145}], "_active": false, "_components": [{"__id__": 155}, {"__id__": 156}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e6fvGTAvNOKYzZOsjYr7pR"}, {"__type__": "cc.Node", "_name": "ContainCTA", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 144}, "_children": [{"__id__": 146}], "_active": true, "_components": [{"__id__": 153}, {"__id__": 154}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -436, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "6eZPFAkoNJC5Zns8ukTiLq"}, {"__type__": "cc.Node", "_name": "Btn_Play", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 145}, "_children": [], "_active": true, "_components": [{"__id__": 147}, {"__id__": 148}, {"__id__": 149}, {"__id__": 150}, {"__id__": 151}, {"__id__": 152}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 165, "y": 35, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1eT63ukjtOZKpcUCJ4ivB+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 146}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 214, "height": 86}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "68Ni4/LT5HXIj+9MLTNz1x"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 146}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fabade8d-544f-4cd7-89cb-0fa3b65145dd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "50vXQG84dIQaQAjHNBZtxj"}, {"__type__": "8c2e3Bq3+tHbIhK6acZcV17", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 146}, "_enabled": true, "__prefab": null, "_id": "4dgEt7riNIbZ2rJIHjmak4"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 146}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": "97bkcmGpdEKbC4uQLGv9sb"}, {"__type__": "961aeGllcFJcaHP0vKDnBY9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 146}, "_enabled": true, "__prefab": null, "ctaNode": {"__id__": 146}, "scaleAmount": 1.1, "duration": 0.5, "useBackEasing": false, "_id": "d3JFGToRxFQZ8TxK4P+LtA"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 146}, "_enabled": true, "__prefab": null, "_alignFlags": 36, "_target": {"__id__": 144}, "_left": 0, "_right": 48, "_top": 0, "_bottom": 36, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "cczA6O5xxGnrFbZFZrsv/L"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 145}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 936, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "72nQYHbKFF26WB1YPhBOVJ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 145}, "_enabled": true, "__prefab": null, "_alignFlags": 44, "_target": null, "_left": -148, "_right": -148, "_top": 0, "_bottom": -56, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 44, "_id": "51NBw6QOlMrILd6qm6LvVe"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 144}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 960}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "14l1Coc2BHVZdXvqJgNsj9"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 144}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "73milp+CpEgKi+jyF5sDCK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 68}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 960}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "47xH/V5oBBY5I8PA+S9A6t"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 68}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 70}, "_alignCanvasWithScreen": true, "_id": "4bJ0SKZzVG2bkzIEIjy395"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 68}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "2bHjpJKklAn4cH/bzn8n5a"}, {"__type__": "2eb04kYUUxAbLBPSFsK/DND", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 68}, "_enabled": true, "__prefab": null, "timer": 30, "containTimer": {"__id__": 94}, "timerLabel": {"__id__": 105}, "timeUPFx": {"__id__": 112}, "btnCTA": {"__id__": 76}, "btnCTA_Bottom": {"__id__": 149}, "endScreenUI": {"__id__": 92}, "bottomUI": {"__id__": 145}, "tutorial": {"__id__": 140}, "_id": "60oDbGtpVIvbaQawGSdZet"}, {"__type__": "99502ByqQ1MoJXT1S4K/kzI", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 68}, "_enabled": true, "__prefab": null, "bg": {"__id__": 59}, "camera": {"__id__": 7}, "containLabel": {"__id__": 94}, "progressUnlockCity": {"__id__": 162}, "tutorial": {"__id__": 123}, "bottomUI": {"__id__": 144}, "btn_CTA": {"__id__": 146}, "_id": "ffcN5yxB5NRpBZOBImaOhQ"}, {"__type__": "0d5ccE1zqVDqLGWFwoIfKDT", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 66}, "_enabled": true, "__prefab": null, "initProgress": 0, "particle": {"__id__": 143}, "_id": "06rg6zrutKCJMwYZ285jJV"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 67}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8f2Cn9e5hNP7rs+g8ZX/Xj"}, {"__type__": "cc.Node", "_name": "City-paris", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 66}, "_children": [{"__id__": 165}, {"__id__": 168}, {"__id__": 171}, {"__id__": 174}, {"__id__": 177}, {"__id__": 180}, {"__id__": 183}, {"__id__": 186}], "_active": false, "_components": [{"__id__": 189}, {"__id__": 190}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -142, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.3, "y": 0.3, "z": 0.33}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "461BaoYf5GRahXniDmnPZ/"}, {"__type__": "cc.Node", "_name": "Base", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 164}, "_children": [], "_active": true, "_components": [{"__id__": 166}, {"__id__": 167}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -18.655, "y": -435.715, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a51knaRfJFtJykhwOqpZmX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 165}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 999, "height": 632}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "52kdYT0rZF/6J4R3Uubmeo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 165}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ddb74b44-ee28-416d-83de-6d153ae7fc1d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "b19Gm8Ze1IFZYB36jIGGnB"}, {"__type__": "cc.Node", "_name": "Sprite-005", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 164}, "_children": [], "_active": true, "_components": [{"__id__": 169}, {"__id__": 170}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 96.123, "y": -28.73, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "72EjfxAotAK5RScWB0oclB"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 168}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 293, "height": 431}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "007JtAN0BCuL74UwOX4v7D"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 168}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "84dcb9dc-b7dd-4856-ae8d-78cbdd97dbc1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "e3IIxGZr5NSqLg0IymPPRW"}, {"__type__": "cc.Node", "_name": "Sprite-006", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 164}, "_children": [], "_active": true, "_components": [{"__id__": 172}, {"__id__": 173}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -211.76, "y": -63.32, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b525XQEAxDYoUSB9k0zsn/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 171}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 138, "height": 303}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b1x1qHJIpJwok9cF6WqFiH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 171}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "db7277dd-ed1e-4472-8feb-a9eacd5c6e02@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "e0LWr93WlNBKpmWbJt49Dz"}, {"__type__": "cc.Node", "_name": "Sprite-003", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 164}, "_children": [], "_active": true, "_components": [{"__id__": 175}, {"__id__": 176}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -300.102, "y": -349.03, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "58Zc3f9jREZK25X5lftk+C"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 174}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 245, "height": 184}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "09wkiBtMFDSa5cBZWRTKeT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 174}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "18afd823-fa44-4b54-8aa2-d32e72b934e4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "b1AO5ZxGZOWZuzRhen09Y4"}, {"__type__": "cc.Node", "_name": "Sprite-002", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 164}, "_children": [], "_active": true, "_components": [{"__id__": 178}, {"__id__": 179}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -360.737, "y": -139.597, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "163VqF9RREA6sscT2WuoaQ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 177}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 219, "height": 285}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a5+u2n+r9IqYMAn4S8NA7t"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 177}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "dc55509b-7f2a-4789-a1fe-143d3118e390@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "905DmiA7FAj58NX21uyzEh"}, {"__type__": "cc.Node", "_name": "Sprite-004", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 164}, "_children": [], "_active": true, "_components": [{"__id__": 181}, {"__id__": 182}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 307.795, "y": -249.45, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4epfEKreVNlqNkrNWhO0fS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 180}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 301, "height": 330}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a8tRorkItEuoa0bL+Ar6mV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 180}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "a07b15a7-21b3-4f24-9ad3-935e5df79c6e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "3eznPLfztGPJ7wcxjLlRme"}, {"__type__": "cc.Node", "_name": "Sprite-007", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 164}, "_children": [], "_active": true, "_components": [{"__id__": 184}, {"__id__": 185}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 327.53, "y": -420.675, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "52OU5DQJRDZ67X3G4TxMXP"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 183}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 125, "height": 162}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "cehH1+uudABpGzRAAajWvG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 183}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8b1c4c1f-aeac-4cb2-a4a1-7f3b2d8b4581@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "ec0dMDTJJOBLdScy2uTu70"}, {"__type__": "cc.Node", "_name": "Sprite-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 164}, "_children": [], "_active": true, "_components": [{"__id__": 187}, {"__id__": 188}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -100.05, "y": -43.917, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a8Ex0Nkv1BMIlOesJ8hi4/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 186}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 322, "height": 570}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "95wlv6W91Cd5GjXQRzPxgy"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 186}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8f34a1dc-76fb-4e0f-a691-042fae2d7da4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "36aiaoi/FBYZgOEISH9gMt"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 164}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e6hHMhGo5C06JL9S/RQUpW"}, {"__type__": "40b5eZgRRpJQ5L3bs2+GgRG", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 164}, "_enabled": true, "__prefab": null, "isUnlock": false, "_id": "29hLhFabNFt4V81yD9iV8M"}, {"__type__": "cc.Node", "_name": "City-shanghai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 66}, "_children": [{"__id__": 192}, {"__id__": 195}, {"__id__": 198}, {"__id__": 201}, {"__id__": 204}, {"__id__": 207}], "_active": true, "_components": [{"__id__": 210}, {"__id__": 211}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -142, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.3, "y": 0.3, "z": 0.3}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5fAnj2dxhNeIXM1IdSqqe8"}, {"__type__": "cc.Node", "_name": "Base", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 191}, "_children": [], "_active": true, "_components": [{"__id__": 193}, {"__id__": 194}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -461.823, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "34vbzbHzFIjLB9o/wGq7vW"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 192}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 958, "height": 595}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f9+3luq+9M0IqEPntEUZ0u"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 192}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bd943698-3dba-46ab-91a1-746d1dd38008@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "b11i8ZAqdFHL17edtRObSF"}, {"__type__": "cc.Node", "_name": "2-5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 191}, "_children": [], "_active": true, "_components": [{"__id__": 196}, {"__id__": 197}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 11.97, "y": -66.12, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "08ySWJD9tIZ4Sxk172DHwE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 195}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 205, "height": 550}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "03DBgjNGdNWaWVFCIbnI7o"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 195}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b0293ada-b553-4610-8817-e87e8f35154f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "0eCJ2O1UhC+YoiBkxuVVw5"}, {"__type__": "cc.Node", "_name": "Sprite-006", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 191}, "_children": [], "_active": true, "_components": [{"__id__": 199}, {"__id__": 200}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 292.163, "y": -155.047, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "efGfOLMPJLiYCkJgVCRt33"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 198}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 308, "height": 583}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f6rw2Ol2xKxpu0R2/zGNmh"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 198}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ae55aae8-fbb0-4fe0-9945-bd997199f94e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "40D/vgE6JHX5X8+M9zSRdz"}, {"__type__": "cc.Node", "_name": "Sprite-004", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 191}, "_children": [], "_active": true, "_components": [{"__id__": 202}, {"__id__": 203}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -292.567, "y": -201.723, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b7hMUfuCRNZo4sXgHxygil"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 201}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 314, "height": 322}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d4NBUBjcBIsrgCsgGMnBmc"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 201}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f61268f6-6ea8-4c4a-b5dd-b3f85dfa8cd2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "bapiV3GHFGAIk4LAEwKnFE"}, {"__type__": "cc.Node", "_name": "Sprite-003", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 191}, "_children": [], "_active": true, "_components": [{"__id__": 205}, {"__id__": 206}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -164.677, "y": -76.47, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "6e+ietjgROl7V7qOKXYbqp"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 204}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 247, "height": 688}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1cAwdP26hM07RfmUIfPRqm"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 204}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "a4f1df9c-3172-4e9f-bae7-341c75748d0d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "47Dtm4kgBF6bTzSDLuI4T9"}, {"__type__": "cc.Node", "_name": "Sprite-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 191}, "_children": [], "_active": true, "_components": [{"__id__": 208}, {"__id__": 209}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 205.08, "y": -359.207, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1eZM5C7rZPG4Qz/AnXjB/t"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 207}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 202, "height": 242}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b5y4fH0PhBE74ojwO1uImV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 207}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fcb8ec70-e809-4bbe-aea0-df71ca40b1a9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "f9FT8deBNLlJOGrgggP4oc"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 191}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9fa2HncmBOG5awkRi1NZwW"}, {"__type__": "40b5eZgRRpJQ5L3bs2+GgRG", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 191}, "_enabled": true, "__prefab": null, "isUnlock": false, "_id": "cbWxCuYdJCS5NwgB0L+GxI"}, {"__type__": "cc.Node", "_name": "City-hanoi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 66}, "_children": [{"__id__": 213}, {"__id__": 216}, {"__id__": 219}, {"__id__": 222}, {"__id__": 225}, {"__id__": 228}, {"__id__": 231}], "_active": false, "_components": [{"__id__": 234}, {"__id__": 235}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -142, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.3, "y": 0.3, "z": 0.3}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "82HZ6XTzRAn6icWB7dxPoQ"}, {"__type__": "cc.Node", "_name": "Base", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 212}, "_children": [], "_active": true, "_components": [{"__id__": 214}, {"__id__": 215}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -387.683, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9cqfo0SPdAp4cTcpCuT36R"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 213}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 960, "height": 818}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "02ucgksllPqJGD5hFO19ZH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 213}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0d52aac7-9b0b-4e61-ac9b-5ba84f465e35@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "89afmFp/5CeYcVO8rxP4a7"}, {"__type__": "cc.Node", "_name": "Sprite-006", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 212}, "_children": [], "_active": true, "_components": [{"__id__": 217}, {"__id__": 218}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 215.303, "y": -87.883, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a6DnyuPI5NtLoMTV4ChWm+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 216}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 293, "height": 354}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "53WhmZKdpGsZVD168cXRhC"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 216}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "54d34845-d1d4-4be7-b02c-84f74064e2fb@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "91E6vyMOxPVqD/hcPDFBvH"}, {"__type__": "cc.Node", "_name": "Sprite-005", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 212}, "_children": [], "_active": true, "_components": [{"__id__": 220}, {"__id__": 221}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 22.687, "y": -301.207, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2diDEp8a5KyY5dn7mhJ+Oh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 219}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 222, "height": 126}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e4T9gV5WxFE7CnYt9Sxcy5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 219}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f86cbe01-3958-4ea2-aeec-ff5a24861990@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "c5zY/l3ZBIA7N7aucXB48x"}, {"__type__": "cc.Node", "_name": "4-7", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 212}, "_children": [], "_active": true, "_components": [{"__id__": 223}, {"__id__": 224}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -245.9, "y": -77.357, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "93zS9LYXlBm5rhnlg1paI5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 222}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 468, "height": 445}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "39X1oP0ZlALZLhnevu4YCi"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 222}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1daf4fef-42c8-4efa-b9c7-3ddc2f891b1a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "b8Of8dlj5DSJjKgtL5RcMJ"}, {"__type__": "cc.Node", "_name": "Sprite-003", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 212}, "_children": [], "_active": true, "_components": [{"__id__": 226}, {"__id__": 227}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 311.85, "y": -317.003, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "caj8QQfdlJG7J0Bz/Fbkna"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 225}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 302, "height": 403}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "73mk1sLOBPyaXAzKP+RpKV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 225}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b1b6966e-659a-4df3-958e-4f004a6ea22d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "82AfIn0PtNRZ2qXv/f4B8w"}, {"__type__": "cc.Node", "_name": "Sprite-002", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 212}, "_children": [], "_active": true, "_components": [{"__id__": 229}, {"__id__": 230}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -90.347, "y": -450.537, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "92meWjnZpPj47Roc3eAp36"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 228}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 229, "height": 224}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b38GL3FERJf4mbeGZ4Muti"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 228}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "86c9a895-d84f-4227-8b38-93977c3a05b3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "3bqmn6pcxIMbRJMrmGLVKC"}, {"__type__": "cc.Node", "_name": "Sprite-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 212}, "_children": [], "_active": true, "_components": [{"__id__": 232}, {"__id__": 233}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -285.187, "y": -310.817, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "122q91fRBIoZ4HmNulFpsc"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 231}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 246, "height": 264}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4eJEZxrG9IUp6dIDlol7Y7"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 231}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d969c942-32a5-4537-ae59-11141086e3a4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": "36hS7HmCZKEoOp4G9OQZVY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "aepA0dp8hEzqPgJsBcsQAH"}, {"__type__": "40b5eZgRRpJQ5L3bs2+GgRG", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": null, "isUnlock": false, "_id": "05nEBQBYxO1r/ErU/fUND1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 66}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "3f+vHWSn9IjpJ9HoMN7WpJ"}, {"__type__": "cc.Node", "_name": "GameData", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 8}, "_children": [], "_active": true, "_components": [{"__id__": 238}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d8e7WUny5JPoUeYn52f+q8"}, {"__type__": "1a265gtPM5Ns4UIKBwxfDS1", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 237}, "_enabled": true, "__prefab": null, "_id": "c0K54PYQNA5IexFK99v410"}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "b6f1529f-418e-42de-a561-84f8c28ff6a0", "instance": null, "targetOverrides": null}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 241}, "shadows": {"__id__": 242}, "_skybox": {"__id__": 243}, "fog": {"__id__": 244}, "octree": {"__id__": 245}, "lightProbeInfo": {"__id__": 246}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.8627450980392157, "y": 0.9254901960784314, "z": 0.9882352941176471, "w": 0.1}, "_skyIllumLDR": 0.1, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.618555, "y": 0.577848, "z": 0.544564, "w": 0}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 1024, "y": 1024}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": false, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null}]