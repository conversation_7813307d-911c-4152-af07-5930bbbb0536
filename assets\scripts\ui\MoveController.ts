import { _decorator, CCInteger, Component, Node, math, Label } from 'cc';
import { BlockController } from '../game/controller/BlockController';
import { GameManager } from '../core/GameManager';
import { GameState } from '../enums/GameState';
const { ccclass, property } = _decorator;

@ccclass('MoveController')
export class MoveController extends Component {

    @property(CCInteger)
    private maxMoveCount: number = 10;

    @property(Label)
    private lblMoveCount: Label = null!;

    private _moveCount: number = 0;

    start() {

        BlockController.onRelease.on(this.useMoveCount, this);

        this._moveCount = this.maxMoveCount;
        this.updateVisual();
    }

    protected onDestroy(): void {
        BlockController.onRelease.off(this.useMoveCount);
    }

    public useMoveCount() {
        this._moveCount--;
        if (this._moveCount <= 0 ) {
            GameManager.Instance.setGameState(GameState.Lose);
            
            console.log('lose');
        }
        console.log('move count', this._moveCount);
        this.updateVisual();
    }

    private updateVisual() {
        const text = math.clamp(this._moveCount, 0, this.maxMoveCount).toString();
        this.lblMoveCount.string = text;
    }
}


