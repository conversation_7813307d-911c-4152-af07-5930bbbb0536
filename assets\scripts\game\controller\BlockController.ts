import { _decorator, Component, Node, <PERSON><PERSON>d<PERSON><PERSON>, <PERSON>sh<PERSON><PERSON><PERSON>, <PERSON>ec3, Vec2, Material, PhysicsSystem, geometry, tween, V<PERSON><PERSON>, director, ERigidBodyType } from 'cc';
import { ElementDataConfig } from '../../enums/LevelDataConfig';
import { BoardController } from '../../app/BoardController';
import { ColorConfigs } from '../board/ColorConfigs';
import { SoundManager } from '../../app/SoundManager';
import { ColliderUnit } from './ColliderUnit';
import { GateController } from './GateController';
import { GameConstantsValue } from '../../enums/GameConfig';
import { Direction } from '../../enums/Enums';
import { ProgressBarUI } from '../../city/ProgressBarUI';
import { GameData } from '../../city/GameData';
import { ProgressUnlockCity } from '../../city/ProgressUnlockCity';
import { StepCount } from '../../app/StepCount';
import { ContentMemeUI } from '../../ui/ContentMemeUI';
import { Signal } from '../../eventSystem/Signal';

const { ccclass, property } = _decorator;

@ccclass('BlockController')
export class BlockController extends Component {
    @property(RigidBody) rb: RigidBody = null!;
    @property(Node) starParent: Node = null!;
    @property(Node) visualParent: Node = null!;
    @property(Node) memeParent: Node = null!;
    @property(Node) colHolder: Node = null!;

    private _boardController: BoardController = null!;
    private _originalMaterial: Material = null!;
    private _outlineMaterial: Material = null!;
    private _model: ElementDataConfig = null!;
    private offsetMoveOutGate = 0.5;
    private _meshRenderers: MeshRenderer[] = [];
    private _originalY: number = 0;
    private _isSelected: boolean = false;

    public get ElementModel() { return this._model; }
    public get ColUnits() { return this.colHolder.children; }

    public static onRelease = new Signal<BlockController>();

    public initialize(config: ElementDataConfig, boardController: BoardController) {
        this._model = config;
        this._originalMaterial = new Material();
        this._originalMaterial.copy(ColorConfigs.Instance.getOriginalMaterial(config.Color));
        this._outlineMaterial = new Material();
        this._outlineMaterial.copy(ColorConfigs.Instance.getOutlineMaterial(config.Color));
        this._meshRenderers = this.visualParent.getComponentsInChildren(MeshRenderer);
        this._meshRenderers.forEach(m => m.material = this._originalMaterial);
        this.starParent.active = config.HasStar;

        if (config.HasMeme) {
            this.memeParent.active = true;
            const texture = ColorConfigs.Instance.getTextureMemeFromColor(config.Color);
            this.memeParent.children.forEach(meme => {
                meme.getComponent(MeshRenderer).material.setProperty('mainTexture', texture);
                meme.setWorldRotationFromEuler(-90, -180, 0);
            });
        }
        else {
            this.memeParent.active = false;
        }

        this.rb.clearState();
        // this.rb.useCCD = true;
        this._boardController = boardController;
        this.rb.linearFactor = new Vec3(1, 0, 1);
        this.rb.type = ERigidBodyType.KINEMATIC;
    }

    public onSelect() {
        this._isSelected = true;
        this._originalY = this.node.worldPosition.y;

        // this.rb.linearFactor = new Vec3(1, 0, 1);
        // this.rb.useCCD = true;
        this.rb.type = ERigidBodyType.DYNAMIC;
        this.rb.linearDamping = 0.1;
        this.rb.angularDamping = 0.9;
        this.enableOutline(true);
        // this.rb.sleep();

        // const currentPos = this.node.worldPosition;
        // this.node.worldPosition = new Vec3(currentPos.x, this._originalY + 1, currentPos.z);

        SoundManager.Instance.playSfx(SoundManager.Instance.Select_Block);
    }

    public onDeselected() {
        this._isSelected = false;
        this.rb.type = ERigidBodyType.KINEMATIC;
        // this.rb.linearFactor = Vec3.ZERO;
        this.rb.linearDamping = 0.5;
        this.enableOutline(false);

        this.rb.sleep();
        this.node.worldPosition = this.GetSnapPosition();
        SoundManager.Instance.playSfx(SoundManager.Instance.Deselect_Block);
        StepCount.Instance?.addStepCount();

        this.checkCleanElement();

        BlockController.onRelease?.trigger(this);
    }

    public movePosition(velocity: Vec3) {
        const smoothedVelocity = velocity.clone();
        if (this._isSelected) {
            smoothedVelocity.y = 0;
            const currentPos = this.node.worldPosition;
            const targetY = this._originalY + 1;
            if (Math.abs(currentPos.y - targetY) > 0.1) {
                smoothedVelocity.y = (targetY - currentPos.y) * 10;
            }
        } else {
            smoothedVelocity.y = 0;
        }
        this.rb.setLinearVelocity(smoothedVelocity);
        this.rb.wakeUp();
    }

    public clearMotion() {
        this.rb.setLinearVelocity(Vec3.ZERO);
        this.rb.clearState();
    }

    public canMoveToPosition(gateController: GateController): boolean {
        if (!this.rb || !this.ColUnits.length) return false;
        const targetGatePosition = gateController.node.worldPosition.clone();
        const mask = PhysicsSystem.PhysicsGroup.DEFAULT;
        for (const unitNode of this.ColUnits) {
            const rayOrigin = unitNode.worldPosition.clone();
            const directionToTarget = targetGatePosition.clone().subtract(rayOrigin);
            const distanceToTarget = directionToTarget.length();
            if (distanceToTarget < 0.01) continue;
            directionToTarget.normalize();
            const ray = new geometry.Ray(rayOrigin.x, rayOrigin.y, rayOrigin.z, directionToTarget.x, directionToTarget.y, directionToTarget.z);
            if (PhysicsSystem.instance.raycastClosest(ray, mask, distanceToTarget, false)) {
                const hitNode = PhysicsSystem.instance.raycastClosestResult.collider.node;
                const hitElementController = hitNode.getComponent(BlockController);
                if (hitElementController && hitElementController !== this) {
                    return false;
                }
            }
        }
        return true;
    }

    // --- Private methods ---
    private enableOutline(enabled: boolean) {
        this._meshRenderers.forEach(m => m.material = enabled ? this._outlineMaterial : this._originalMaterial);
    }

    private GetSnapPosition(): Vec3 {
        const colUnits = this.colHolder.children;
        if (!colUnits.length) return this.node.worldPosition.clone();
        const arrWorldPosXY = colUnits.map(u => new Vec2(u.getWorldPosition().x, u.getWorldPosition().z));
        const center = this._boardController.getBoundingRectCenter(arrWorldPosXY);
        return new Vec3(center.x, 1, center.z);
    }

    private checkCleanElement() {
        const gateController = this.FindValidGate();
        if (gateController) {
            this.processElementClear(gateController);
        }
    }

    private FindValidGate(): GateController | null {
        for (const colUnit of this.colHolder.children) {
            const gateController = colUnit.getComponent(ColliderUnit).checkColliderGate();
            if (!gateController) continue;
            for (const gate of gateController) {
                if (gate.checkElementCanPass(this)) {
                    return gate;
                }
            }
        }
        return null;
    }

    private getPosMoveOutGate(gateController: GateController): Vec3 {
        // console.log('[BlockController] getPosMoveOutGate', gateController.node.worldPosition);
        const dir = gateController.Model.Direction;
        const gatePos = gateController.node.worldPosition;
        const curPos = this.node.worldPosition;
        let targetX = curPos.x, targetY = curPos.z, targetZ = curPos.y;
        if (dir === Direction.Horizontal) {
            targetX = gatePos.x + (gatePos.x > curPos.x ? this.offsetMoveOutGate : -this.offsetMoveOutGate);
        } else {
            targetY = gatePos.z + (gatePos.z > curPos.z ? this.offsetMoveOutGate : -this.offsetMoveOutGate);
        }
        const result = new Vec3(targetX, targetZ, targetY);
        // console.log('[BlockController] getPosMoveOutGate result', result);
        return result;
    }

    private processElementClear(gateController: GateController) {
        // console.log('[BlockController] processElementClear', gateController.node.worldPosition);

        this.memeParent.active = false;

        for (const colUnit of this.colHolder.children) {
            const pos = colUnit.worldPosition.clone();

            ContentMemeUI.Instance.createMeme(pos, this._model.Color);

        }


        const targetPosition = this.getPosMoveOutGate(gateController);
        this.rb?.setLinearVelocity(Vec3.ZERO);
        this.rb?.clearForces();
        this.rb?.clearState();
        gateController.openGate();
        this.setClipPlane(targetPosition);
        const cellSize = GameConstantsValue.CellSize;
        const isHorizontal = gateController.Model.Direction === Direction.Horizontal;
        let minX = Infinity, maxX = -Infinity, minZ = Infinity, maxZ = -Infinity;
        for (const cellUnitNode of this.ColUnits) {
            const cellPos = cellUnitNode.worldPosition.clone();
            minX = Math.min(minX, cellPos.x);
            maxX = Math.max(maxX, cellPos.x);
            minZ = Math.min(minZ, cellPos.z);
            maxZ = Math.max(maxZ, cellPos.z);
        }
        const elementWidth = maxX - minX + cellSize;
        const elementLength = maxZ - minZ + cellSize;
        const curPos = this.node.worldPosition;
        const direction = new Vec3(targetPosition.x - curPos.x, targetPosition.y - curPos.y, targetPosition.z - curPos.z).normalize();
        const extraDistance = isHorizontal ? elementWidth : elementLength;
        const finalPosition = new Vec3(
            targetPosition.x + direction.x * extraDistance,
            targetPosition.y,
            targetPosition.z + direction.z * extraDistance
        );
        // console.log('[BlockController] processElementClear tween to', finalPosition);
        tween(this.node)
            .call(() => SoundManager.Instance.playSfx(SoundManager.Instance.Clear_Block))
            .to(0.5, { worldPosition: finalPosition }, {
                onComplete: () => {
                    if (this.node?.isValid) {
                        this.node.destroy();
                        // this.updateProgressUnlockCity();
                    }
                }
            })
            .start();
    }

    private updateProgressUnlockCity() {
        if (GameData.instance.getSelectedCityID() < 3) ProgressUnlockCity.Instance.unlockCurrentCity(1);
        else ProgressBarUI.Instance.updateCurrentValue(1);
    }

    private setClipPlane(posMoveOutGate: Vec3) {
        const curPos = this.node.worldPosition;
        const direction = new Vec3(posMoveOutGate.x - curPos.x, posMoveOutGate.y - curPos.y, posMoveOutGate.z - curPos.z);
        const absX = Math.abs(direction.x), absY = Math.abs(direction.y), absZ = Math.abs(direction.z);
        let mainAxis = '', axisValue = 0;
        if (absX > absY && absX > absZ) { mainAxis = 'X'; axisValue = direction.x; }
        else if (absY > absX && absY > absZ) { mainAxis = 'Y'; axisValue = direction.y; }
        else { mainAxis = 'Z'; axisValue = direction.z; }
        const moveDirection = axisValue > 0 ? 'positive' : 'negative';
        const clipPlane = new Vec4(0, 0, 0, 0);
        if (mainAxis === 'X') {
            clipPlane.w = Math.abs(posMoveOutGate.x);
            clipPlane.x = moveDirection === 'positive' ? -1 : 1;
        } else if (mainAxis === 'Z') {
            clipPlane.w = Math.abs(posMoveOutGate.z);
            clipPlane.z = moveDirection === 'positive' ? -1 : 1;
        }
        this.visualParent.getComponentsInChildren(MeshRenderer).forEach(mesh => mesh.material.setProperty('clipPlane', clipPlane));
        this.starParent.getComponentsInChildren(MeshRenderer).forEach(star => star.material.setProperty('clipPlane', clipPlane));
    }

    private setMemeImage() {


    }
}
