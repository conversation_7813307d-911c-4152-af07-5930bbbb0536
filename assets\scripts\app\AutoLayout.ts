import { _decorator, Component, Node, view, Vec3, Widget } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('AutoLayout')
export class AutoLayout extends Component {
    @property(Node) bg: Node = null!;
    @property(Node) city: Node = null!;
    @property(Node) bottomBtnUI: Node = null!;
    @property(Node) ic_Left: Node = null!;
    @property(Node) btn_CTA: Node = null!;

    private bottomUI_scale_landscape: Vec3 = new Vec3(1, 1, 1);
    private bottomUI_scale_portrait: Vec3 = new Vec3(0.6, 0.6, 0.6);
    private city_scale_landscape: Vec3 = new Vec3(1, 1, 1);
    private city_scale_portrait: Vec3 = new Vec3(0.7, 0.7, 0.7);

    onLoad() {
        this.updateLayout();
        view.setResizeCallback(this.updateLayout.bind(this));
    }

    onDestroy() {
        view.setResizeCallback(null);
    }

    private updateLayout() {
        const frameSize = view.getFrameSize();
        const isLandscape = frameSize.width > frameSize.height;
        if (!this.bg || !this.city) return;

        // BG
        this.bg.children[0].active = !isLandscape;
        this.bg.children[1].active = isLandscape;

        // City & UI scale
        this.city.setScale(isLandscape ? this.city_scale_landscape : this.city_scale_portrait);
        this.bottomBtnUI.setScale(isLandscape ? this.bottomUI_scale_landscape : this.bottomUI_scale_portrait);

        // Widget positions
        this.ic_Left.getComponent(Widget).bottom = 24;
        this.btn_CTA.getComponent(Widget).bottom = 36;
        if (isLandscape) {
            this.ic_Left.getComponent(Widget).left = frameSize.width / 2.2;
            this.btn_CTA.getComponent(Widget).right = frameSize.width / 2.2;
        } else {
            this.ic_Left.getComponent(Widget).left = 48;
            this.btn_CTA.getComponent(Widget).right = 48;
        }
    }
}