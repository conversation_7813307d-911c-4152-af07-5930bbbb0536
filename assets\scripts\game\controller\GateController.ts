import { _decorator, Component, instantiate, Material, MeshRenderer, Node, Texture2D, tween, Vec3 } from 'cc';
import { EffectCube } from '../../common/EffectCube';
import { GateDataConfig } from '../../enums/LevelDataConfig';
import { ColorConfigs } from '../board/ColorConfigs';
import { BlockController } from './BlockController';
import { Direction, GateType } from '../../enums/Enums';
import { GameConstantsValue } from '../../enums/GameConfig';

const { ccclass, property } = _decorator;

@ccclass('GateController')
export class GateController extends Component {
    @property(MeshRenderer) private meshRenderer: MeshRenderer = null!;
    @property(Node) private particleParent: Node = null!;
    @property(Node) private starParent: Node = null!;

    private _particleSystems: EffectCube[] = [];
    private _model: GateDataConfig = null!;
    private readonly Tolerance: number = 0.05;

    public get Model(): GateDataConfig { return this._model; }

    public initialize(gateConfig: GateDataConfig): void {
        this.node.setPosition(gateConfig.Position);
        this.node.setRotationFromEuler(gateConfig.Rotation);
        this._particleSystems = this.particleParent.children.map(child => child.getComponent(EffectCube));
        const texture = ColorConfigs.Instance.getTextureFromColor(gateConfig.Color);
        if (texture) {
            this.meshRenderer.material.setProperty('mainTexture', texture);
            this.setParticleSystemColor(texture);
        }
        this.starParent.active = gateConfig.HasStar;
        this._model = gateConfig;
    }

    public checkElementCanPass(element: BlockController): boolean {
        const willPassResult = this.checkElementWillBeAbleToPass(element);
        if (!willPassResult.canPass || !this.isElementSizeSmallerThanGate(element) || !element.canMoveToPosition(this)) {
            return false;
        }
        return true;
    }

    public checkElementWillBeAbleToPass(element: BlockController): { canPass: boolean, gateBlock: GateType } {
        return element.ElementModel.Color === this._model.Color ? { canPass: true, gateBlock: GateType.None } : { canPass: false, gateBlock: GateType.None };
    }

    private isElementSizeSmallerThanGate(elementController: BlockController): boolean {
        if (!this._model || !this.node) return false;
        const elementColUnit = elementController.ColUnits;
        const cellSize = GameConstantsValue.CellSize;
        const cellHalfSize = cellSize / 2;
        const gateWorldPosition = this.node.worldPosition.clone();
        const gateLength = this._model.Size * cellSize;
        const gateHalfLength = gateLength / 2;
        const isHorizontal = this._model.Direction === Direction.Horizontal;
        let minX = Infinity, maxX = -Infinity, minZ = Infinity, maxZ = -Infinity;
        for (const cellUnitNode of elementColUnit) {
            const cellPos = cellUnitNode.worldPosition.clone();
            minX = Math.min(minX, cellPos.x);
            maxX = Math.max(maxX, cellPos.x);
            minZ = Math.min(minZ, cellPos.z);
            maxZ = Math.max(maxZ, cellPos.z);
        }
        const gateMin = isHorizontal ? (gateWorldPosition.z - gateHalfLength) : (gateWorldPosition.x - gateHalfLength);
        const gateMax = isHorizontal ? (gateWorldPosition.z + gateHalfLength) : (gateWorldPosition.x + gateHalfLength);
        for (const cellUnitNode of elementColUnit) {
            const cellPos = cellUnitNode.worldPosition.clone();
            const cellMin = isHorizontal ? (cellPos.z - cellHalfSize) : (cellPos.x - cellHalfSize);
            const cellMax = isHorizontal ? (cellPos.z + cellHalfSize) : (cellPos.x + cellHalfSize);
            if (cellMax > gateMax + this.Tolerance || cellMin < gateMin - this.Tolerance) return false;
        }
        return true;
    }

    public openGate(): void {
        tween(this.node).stop();
        const currentPos = this.node.position;
        tween(this.node)
            .to(0.15, { position: new Vec3(currentPos.x, -1.5, currentPos.z) })
            .call(() => this.playParticleSystem())
            .delay(0.3)
            .call(() => { this.stopParticleSystem(); this.closeGate(); })
            .start();
    }

    public closeGate(): void {
        tween(this.node).stop();
        const currentPos = this.node.position;
        tween(this.node)
            .to(0.5, { position: new Vec3(currentPos.x, 0, currentPos.z) })
            .start();
    }

    public playParticleSystem(): void {
        this._particleSystems.forEach(particleSystem => particleSystem.Play());
    }

    public stopParticleSystem(): void {
        this._particleSystems.forEach(particleSystem => particleSystem.Stop());
    }

    private setParticleSystemColor(texture: Texture2D): void {
        this._particleSystems.forEach(particleSystem => particleSystem.setTexture(texture));
    }
}


