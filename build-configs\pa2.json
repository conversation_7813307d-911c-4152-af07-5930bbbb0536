{"name": "pa2", "platform": "web-mobile", "buildPath": "project://build", "debug": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "polyfills": {"asyncFunctions": true, "coreJs": false}, "experimentalEraseModules": false, "useBuiltinServer": false, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": false, "packAutoAtlas": true, "startScene": "9afa144c-a7a9-4403-b751-d9b05cc43985", "outputName": "web-mobile", "scenes": [{"url": "db://assets/scenes/pa2.scene", "uuid": "9afa144c-a7a9-4403-b751-d9b05cc43985", "inBundle": false}, {"url": "db://assets/scenes/pa2-gameplay.scene", "uuid": "b6f1529f-418e-42de-a561-84f8c28ff6a0", "inBundle": false}], "web-mobile": [], "bundleConfigs": [], "packages": {"web-mobile": {"orientation": "auto", "embedWebDebugger": false, "cullEngineAsmJsModule": true, "__version__": "1.0.1"}, "cocos-service": {"configID": "0e26b4", "services": [], "__version__": "3.0.7"}}, "__version__": "1.3.5"}