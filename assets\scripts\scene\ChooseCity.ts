import { _decorator, Component, Node, tween, Vec3 } from 'cc';
import { ButtonChoosen } from '../ui/ChooseCity/ButtonChoosen';
import { UnlockCity } from '../city/UnlockCity';
import { DirectionHand } from '../app/DirectionHand';
import { OneClickOption } from '../app/OneClickOption';
const { ccclass, property } = _decorator;

@ccclass('ChooseCity')
export class ChooseCity extends Component {

    @property(Node) tutorialNode: Node = null!;

    @property(OneClickOption) oneClickOption: OneClickOption = null!;

    private _currentIndex: number = 0;
    private _isPlaying: boolean = false;
    private _tutorial: DirectionHand = null;
    private _buttonComponents: ButtonChoosen[] = [];

    start(): void {
        this.setupTutorial();
        this.setupButtonListeners();
        this.playAnimation();
    }

    private setupTutorial(): void {
        if (this.tutorialNode) {
            this._tutorial = this.tutorialNode.getComponent(DirectionHand);
        }
    }

    private setupButtonListeners(): void {
        this._buttonComponents = this.node.children
            .map(node => node.getComponent(ButtonChoosen))
            .filter(button => button !== null);

        this._buttonComponents.forEach(button => {
            
            if(this.oneClickOption)
            {
                button.onButtonClick = () => this.oneClickOption.actionOneClick();
            }
            else
            {
                button.onButtonClick = () => this.onButtonSelected(button);
            }
        });
    }

    private onButtonSelected(selectedButton: ButtonChoosen): void {
        this.updateButtonStates(selectedButton);
        this.stopAnimation();
    }

    private updateButtonStates(selectedButton: ButtonChoosen): void {
        this._buttonComponents.forEach(button => {
            if (button === selectedButton) {
                button.unlockCity.UnlockAll();
            } else {
                button.unlockCity.LockAll();
            }
        });
    }

    public playAnimation(): void {
        if (this._isPlaying) return;

        this._isPlaying = true;
        this._currentIndex = 0;
        this.playNextCity();
    }

    private playNextCity(): void {
        if (!this._isPlaying) return;

        this.resetIndexIfNeeded();

        const currentCity = this.getCurrentCityNode();
        const unlockCity = currentCity?.getComponent(UnlockCity);

        if (unlockCity) {
            this.animateCurrentCity(unlockCity, currentCity);
        } else {
            this.moveToNextCity();
        }
    }

    private resetIndexIfNeeded(): void {
        if (this._currentIndex >= this.node.children.length) {
            this._currentIndex = 0;
        }
    }

    private getCurrentCityNode(): Node | null {
        const cityContainer = this.node.children[this._currentIndex];
        return cityContainer?.children[0] || null;
    }

    private animateCurrentCity(unlockCity: UnlockCity, currentCity: Node): void {
        unlockCity.UnlockAll();
        this.moveTutorialToCity(currentCity);

        this.scheduleOnce(() => {
            if (this._isPlaying) {
                unlockCity.LockAll();
                this.moveToNextCity();
            }
        }, 1.5);
    }

    private moveTutorialToCity(currentCity: Node): void {
        if (!this._tutorial) return;

        const targetPos = currentCity.getWorldPosition();
        const tutorialTargetPos = new Vec3(targetPos.x, targetPos.y - 75, targetPos.z);

        tween(this._tutorial.node)
            .to(0.3, { worldPosition: tutorialTargetPos }, { easing: 'quadOut' })
            .call(() => this._tutorial.animation.play())
            .start();
    }

    private moveToNextCity(): void {
        this._currentIndex++;
        this.playNextCity();
    }

    public stopAnimation(): void {
        this._isPlaying = false;
        this.unscheduleAllCallbacks();
    }
}


