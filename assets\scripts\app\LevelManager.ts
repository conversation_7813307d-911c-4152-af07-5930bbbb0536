import { _decorator, CCInteger, Component, JsonAsset, Node } from 'cc';
import { LevelDataConfig } from '../enums/LevelDataConfig';
const { ccclass, property } = _decorator;

@ccclass('LevelManager')
export class LevelManager extends Component {

    @property(JsonAsset) jsonData: JsonAsset = null;
    private _levelDataConfigs: LevelDataConfig[] = [];

    public initialize() {
        this.loadLevelDataConfig();
    }

    private loadLevelDataConfig(): void {
        const jsonData = this.jsonData.json;
        const levelDataConfigs = jsonData.map(item => new LevelDataConfig(item));
        this._levelDataConfigs = levelDataConfigs;
    }

    public loadLevel(index: number): LevelDataConfig {
        const levelDataConfig = this._levelDataConfigs[index];
        return levelDataConfig;
    }
}


