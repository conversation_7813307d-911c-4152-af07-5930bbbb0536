// Effect Syntax Guide: https://docs.cocos.com/creator/manual/zh/shader/index.html

CCEffect %{
  techniques:
  - name: opaque
    passes:
    - switch: USE_OUTLINE_PASS
      vert: silhouette-edge-vs
      frag: silhouette-edge-fs
      rasterizerState:
        cullMode: front
      depthStencilState:
        depthFunc: less_equal
        depthTest: true
        depthWrite: true
      properties:
        lineWidth:     { value: 80, target: outlineParams.x }
        depthBias:     { value: 0,  target: outlineParams.y }
        outlineColor:  { value: [1.0, 1.0, 1.0, 1.0], target: baseColor, editor: { displayName: Outline Color, type: color } }
        outlineMap:    { value: grey, target: baseColorMap }
    - vert: standard-vs
      frag: standard-fs
      properties: &props
        mainTexture:              { value: grey, target: albedoMap, editor: { displayName: AlbedoMap } }
        mainColor:                { value: [1.0, 1.0, 1.0, 1.0], target: albedo, linear: true, editor: { displayName: Albedo, type: color } }
        albedoScale:              { value: [1.0, 1.0, 1.0], target: albedoScaleAndCutoff.xyz }
        alphaThreshold:           { value: 0.5, target: albedoScaleAndCutoff.w, editor: { parent: USE_ALPHA_TEST, slide: true, range: [0, 1.0], step: 0.001 } }
        roughness:                { value: 0.8, target: pbrParams.y, editor: { slide: true, range: [0, 1.0], step: 0.001 } }
        metallic:                 { value: 0.6, target: pbrParams.z, editor: { slide: true, range: [0, 1.0], step: 0.001 } }
        clipPlane:                { value: [0.0, 0.0, 0.0, 0.0] }
        highlightColor:           { value: [0.0, 0.0, 0.0, 1.0], editor: { displayName: Highlight Color, type: color } }
        highlightParams:          { value: [2.0, 0.5, 0.0, 0.0], editor: { displayName: Highlight Params (x Power, y Intensity) } }
    - vert: shadow-caster-vs
      frag: shadow-caster-fs
      phase: shadow-caster
      propertyIndex: 0
      rasterizerState:
        cullMode: front
      properties:
        mainTexture:    { value: grey, target: albedoMap, editor: { displayName: AlbedoMap } }
        mainColor:      { value: [1.0, 1.0, 1.0, 1.0], target: albedo, linear: true, editor: { displayName: Albedo, type: color } }
        albedoScale:    { value: [1.0, 1.0, 1.0], target: albedoScaleAndCutoff.xyz }
        alphaThreshold: { value: 0.5, target: albedoScaleAndCutoff.w, editor: { parent: USE_ALPHA_TEST } }
}%

CCProgram macro-remapping %{
  #pragma define-meta USE_OUTLINE_PASS
  #pragma define-meta USE_ALPHA_TEST
  #pragma define-meta USE_POSITION_SCALING
}%

CCProgram shared-ubos %{
  uniform Constants {
    vec4 albedo;
    vec4 albedoScaleAndCutoff;
    vec4 pbrParams;
    vec4 clipPlane;
    vec4 highlightColor;
    vec4 highlightParams;
  };
}%

CCProgram outline-ubos %{
  uniform OutlineVert {
    vec4 outlineParams; // x: line width, y: depth hack
  };
  uniform OutlineFrag {
    vec4 baseColor;
  };
}%

CCProgram surface-vertex %{
  #define CC_SURFACES_VERTEX_MODIFY_WORLD_POS
  vec3 SurfacesVertexModifyWorldPos(in SurfacesStandardVertexIntermediate In)
  {
    return In.worldPos;
  }
  
  #define CC_SURFACES_VERTEX_MODIFY_WORLD_NORMAL
  vec3 SurfacesVertexModifyWorldNormal(in SurfacesStandardVertexIntermediate In)
  {
    return In.worldNormal.xyz;
  }
}%

CCProgram surface-fragment %{
  #if USE_ALBEDO_MAP
    uniform sampler2D albedoMap;
    #pragma define-meta ALBEDO_UV options([v_uv, v_uv1])
  #endif

  #define CC_SURFACES_FRAGMENT_MODIFY_BASECOLOR_AND_TRANSPARENCY
  vec4 SurfacesFragmentModifyBaseColorAndTransparency()
  {
    bool useClipPlaneEnabled = clipPlane.x != 0.0 || clipPlane.y != 0.0 || clipPlane.z != 0.0;
    if(useClipPlaneEnabled && dot(FSInput_worldPos.xyz, clipPlane.xyz) + clipPlane.w <= 0.0) {
      discard;
    }

    vec4 baseColor = albedo;
    
    #if USE_ALBEDO_MAP
      vec4 texColor = texture(albedoMap, ALBEDO_UV);
      texColor.rgb = SRGBToLinear(texColor.rgb);
      baseColor *= texColor;
    #endif

    baseColor.rgb *= albedoScaleAndCutoff.xyz;

    // Rim light (highlight ở rìa)
    vec3 viewDir = normalize(cc_cameraPos.xyz - FSInput_worldPos.xyz);
    float rim = 1.0 - max(dot(normalize(FSInput_worldNormal), viewDir), 0.0);
    rim = pow(rim, highlightParams.x); // Độ sắc nét
    baseColor.rgb += rim * highlightColor.rgb * highlightParams.y;

    return baseColor;
  }

  #define CC_SURFACES_FRAGMENT_MODIFY_WORLD_NORMAL
  vec3 SurfacesFragmentModifyWorldNormal()
  {
    return normalize(FSInput_worldNormal);
  }

  #define CC_SURFACES_FRAGMENT_MODIFY_PBRPARAMS
  vec4 SurfacesFragmentModifyPBRParams()
  {
    // ao, roughness, metallic, specularIntensity
    return vec4(1.0, pbrParams.y, pbrParams.z, 0.5);
  }
}%

CCProgram surface-vertex-silhouette-edge %{
  #include <outline-ubos>

  #define CC_SURFACES_VERTEX_MODIFY_LOCAL_POS
  vec3 SurfacesVertexModifyLocalPos(in SurfacesStandardVertexIntermediate In)
  {
    float width = outlineParams.x * 0.001;
    vec3 localPos = In.position.xyz;

    #if USE_POSITION_SCALING
      vec3 dir = normalize(localPos);
      float flip = dot(dir, normalize(In.normal)) < 0.0 ? -1.0 : 1.0;
      localPos += flip * dir * width * 2.0;
    #else
      localPos += normalize(In.normal) * width;
    #endif
    return localPos;
  }

  #define CC_SURFACES_VERTEX_MODIFY_CLIP_POS
  vec4 SurfacesVertexModifyClipPos(in SurfacesStandardVertexIntermediate In)
  {
    vec4 clipPos = In.clipPos;
    float scaleZ = cc_nearFar.z == 0.0 ? 0.5 : 1.0;
    clipPos.z -= outlineParams.y * 0.002 * scaleZ;
    return clipPos;
  }
}%

CCProgram surface-fragment-silhouette-edge %{
  #include <outline-ubos>

  #if USE_BASE_COLOR_MAP
    uniform sampler2D baseColorMap;
  #endif

  #define CC_SURFACES_FRAGMENT_MODIFY_BASECOLOR_AND_TRANSPARENCY
  vec4 SurfacesFragmentModifyBaseColorAndTransparency()
  {
    bool useClipPlaneEnabled = clipPlane.x != 0.0 || clipPlane.y != 0.0 || clipPlane.z != 0.0;
    if(useClipPlaneEnabled && dot(FSInput_worldPos.xyz, clipPlane.xyz) + clipPlane.w <= 0.0) {
      discard;
    }

    vec4 color = vec4(cc_mainLitColor.rgb, 1.0);
    color.rgb = SRGBToLinear(baseColor.rgb);
    #if USE_BASE_COLOR_MAP
      vec4 texColor = texture(baseColorMap, FSInput_texcoord);
      texColor.rgb = SRGBToLinear(texColor.rgb);
      color *= texColor;
    #endif

    return color;
  }
}%

CCProgram standard-vs %{
  precision highp float;
  #include <surfaces/effect-macros/common-macros>
  #include <surfaces/includes/common-vs>
  #include <shared-ubos>
  #include <surface-vertex>
  #include <surfaces/includes/standard-vs>
  #include <shading-entries/main-functions/render-to-scene/vs>
}%

CCProgram standard-fs %{
  precision highp float;
  #include <surfaces/effect-macros/common-macros>
  #include <surfaces/includes/common-fs>
  #include <shared-ubos>
  #include <surface-fragment>
  #include <lighting-models/includes/standard>
  #include <surfaces/includes/standard-fs>
  #include <shading-entries/main-functions/render-to-scene/fs>
}%

CCProgram silhouette-edge-vs %{
  precision highp float;
  #include <surfaces/effect-macros/silhouette-edge>
  #include <surfaces/includes/common-vs>
  #include <shared-ubos>
  #include <surface-vertex-silhouette-edge>
  #include <shading-entries/main-functions/misc/silhouette-edge-vs>
}%

CCProgram silhouette-edge-fs %{
  precision highp float;
  #include <surfaces/effect-macros/silhouette-edge>
  #include <surfaces/includes/common-fs>
  #include <shared-ubos>
  #include <surface-fragment-silhouette-edge>
  #include <shading-entries/main-functions/misc/silhouette-edge-fs>
}%

CCProgram shadow-caster-vs %{
  precision highp float;
  #include <surfaces/effect-macros/render-to-shadowmap>
  #include <surfaces/includes/common-vs>
  #include <shared-ubos>
  #include <surface-vertex>
  #include <shading-entries/main-functions/render-to-shadowmap/vs>
}%

CCProgram shadow-caster-fs %{
  precision highp float;
  #include <surfaces/effect-macros/render-to-shadowmap>
  #include <surfaces/includes/common-fs>
  #include <shared-ubos>
  #include <surface-fragment>
  #include <shading-entries/main-functions/render-to-shadowmap/fs>
}%
