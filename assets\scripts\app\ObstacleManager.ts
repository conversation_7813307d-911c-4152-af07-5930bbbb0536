import { _decorator, Component, Enum, instantiate, Node, Prefab, Quat, Vec3 } from 'cc';
import { ObstacleDataConfig } from '../enums/LevelDataConfig';
import { ObstacleType } from '../enums/Enums';
import { BoardController } from './BoardController';
const { ccclass, property } = _decorator;

@ccclass('ObstaclePrefabItem')
export class ObstaclePrefabItem {
    @property({ type: Enum(ObstacleType), tooltip: "Loại Obstacle." })
    type: ObstacleType = ObstacleType.Corner;

    @property({ type: Prefab, tooltip: "Prefab tương ứng cho loại Obstacle này." })
    prefab: Prefab | null = null;
}


@ccclass('ObstacleManager')
export class ObstacleManager extends Component {

    @property({ type: [ObstaclePrefabItem], tooltip: "List obstacle prefabs" })
    protected obstaclePrefabs: ObstaclePrefabItem[] = [];

    public initialize(obstacles: ObstacleDataConfig[] | null | undefined): void {
        if (!obstacles || obstacles.length === 0) {
            // console.log("ObstaclesView: No obstacles data provided to initialize.");
            return;
        }

        if (this.obstaclePrefabs.length === 0) {
            console.warn("ObstaclesView: obstaclePrefabs array is empty. No prefabs configured to create obstacles.");
        }

        // console.log(`ObstaclesView initializing with ${obstacles.length} obstacles on node ${this.node.name}.`);
        obstacles.forEach(obstacleData => {
            this.createObstacle(obstacleData);
        });
    }

    private createObstacle(obstacleData: ObstacleDataConfig): void {
        const prefabItem = this.obstaclePrefabs.find(item => item.type === obstacleData.Type);
        if (!prefabItem || !prefabItem.prefab) {
            console.error(`ObstacleManager: No prefab found for obstacle type ${obstacleData.Type}.`);
            return;
        }

        const prefab = prefabItem.prefab;
        const obstacle = instantiate(prefab);
        obstacle.setParent(this.node);


        obstacle.setPosition(obstacleData.Position);
        obstacle.setRotationFromEuler(obstacleData.Rotation);
        // console.log(`ObstacleManager: Created obstacle ${obstacleData.Type} at position ${worldPos.x}, ${worldPos.y}, ${worldPos.z} with rotation ${rotation.x}, ${rotation.y}, ${rotation.z}`);
    }

}
