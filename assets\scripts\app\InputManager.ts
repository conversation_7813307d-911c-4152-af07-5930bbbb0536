import { _decorator, Component, Node, PhysicsSystem, Vec3, Camera, Input, EventTouch, geometry, RigidBody, input } from 'cc';
import { BlockController } from '../game/controller/BlockController';
import { GameState } from '../enums/GameState';
import { GameManager } from '../core/GameManager';
const { ccclass, property } = _decorator;

@ccclass('InputManager')
export class InputManager extends Component {
    @property(Node)
    private plane: Node = null;


    @property(Camera)
    private camera: Camera = null;

    @property
    private dragSpeed: number = 25;

    @property
    private maxVelocity: number = 30;

    @property
    private smoothFactor: number = 0.8;

    @property
    private dampingFactor: number = 0.95;

    private lastTouchMoveTime: number = 0;
    private readonly noMoveTimeThreshold: number = 0.1;
    private _lastVelocity: Vec3 = new Vec3();
    private _currentTargetWorldPosition: Vec3 | null = null;

    private _selectedElement: BlockController | null = null;

    public initialize() {
        this.registerInputListeners();
    }

    protected onDestroy(): void {
        this.unregisterInputListeners();
    }

    private registerInputListeners(): void {
        input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
        input.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        input.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        input.on(Input.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
    }

    private unregisterInputListeners(): void {
        input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);
        input.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        input.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        input.off(Input.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
    }

    private onTouchStart(event: EventTouch): void {
        if (GameManager.Instance.getGameState() === GameState.Tutorial) {
            GameManager.Instance.setGameState(GameState.Playing);
        }

        const touchPos = event.getLocation();
        const ray = new geometry.Ray();
        this.camera.screenPointToRay(touchPos.x, touchPos.y, ray);

        if (PhysicsSystem.instance.raycast(ray)) {
            const raycastResults = PhysicsSystem.instance.raycastResults;
            for (const result of raycastResults) {
                const eleComponent = result.collider.node.getComponent(BlockController);
                if (eleComponent) {
                    this._selectedElement = eleComponent;
                    this.lastTouchMoveTime = performance.now() / 1000;

                    const currentPosition = this._selectedElement.node.worldPosition.clone();
                    this._currentTargetWorldPosition = new Vec3(currentPosition.x, this.plane.worldPosition.y, currentPosition.z);

                    this._selectedElement.onSelect();
                    break;
                }
            }
        }
    }

    private onTouchMove(event: EventTouch): void {
        if (!this._selectedElement) return;

        this.lastTouchMoveTime = performance.now() / 1000;

        const currentTouchPos = event.getLocation();
        const ray = new geometry.Ray();
        this.camera.screenPointToRay(currentTouchPos.x, currentTouchPos.y, ray);

        const planeNormal = this.plane.up;
        const pointOnPlane = this.plane.worldPosition;
        const dragPlane = new geometry.Plane();
        geometry.Plane.fromNormalAndPoint(dragPlane, planeNormal, pointOnPlane);

        const distance = geometry.intersect.rayPlane(ray, dragPlane);

        if (distance > 0) {
            const intersectionPoint = new Vec3();
            Vec3.scaleAndAdd(intersectionPoint, ray.o, ray.d, distance);
            this._currentTargetWorldPosition = intersectionPoint;
        }
    }

    private onTouchEnd(): void {
        this.releaseElement();
    }

    private onTouchCancel(): void {
        this.releaseElement();
    }

    private releaseElement(): void {
        if (this._selectedElement) {
            this._selectedElement.clearMotion();
            this._selectedElement.onDeselected();
        }
        this._selectedElement = null;
        this._currentTargetWorldPosition = null;
        this.lastTouchMoveTime = 0;
        this._lastVelocity.set(0, 0, 0);
    }

    update() {
        if (!this._selectedElement || !this._currentTargetWorldPosition) return;

        const targetRigidBody = this._selectedElement.node.getComponent(RigidBody);
        if (!targetRigidBody) return;

        const now = performance.now() / 1000;

        if (now - this.lastTouchMoveTime > this.noMoveTimeThreshold) {
            this._lastVelocity.multiplyScalar(this.dampingFactor);
            if (this._lastVelocity.length() < 0.1) {
                this._selectedElement.clearMotion();
                this._lastVelocity.set(0, 0, 0);
            } else {
                this._selectedElement.movePosition(this._lastVelocity);
            }
        } else {
            const currentPosition = this._selectedElement.node.worldPosition;
            const targetVelocity = new Vec3();
            Vec3.subtract(targetVelocity, this._currentTargetWorldPosition, currentPosition);

            const distance = targetVelocity.length();
            if (distance > 0.01) {
                Vec3.multiplyScalar(targetVelocity, targetVelocity, this.dragSpeed);
                Vec3.lerp(this._lastVelocity, this._lastVelocity, targetVelocity, this.smoothFactor);

                const currentSpeed = this._lastVelocity.length();
                if (currentSpeed > this.maxVelocity) {
                    Vec3.multiplyScalar(this._lastVelocity, this._lastVelocity, this.maxVelocity / currentSpeed);
                }

                this._selectedElement.movePosition(this._lastVelocity);
            }
        }
    }
}


