import { _decorator, Camera, Component, Node, Prefab, Vec3, instantiate, Sprite, UITransform, tween } from 'cc';
import { ColorType } from '../enums/Enums';
import { ColorConfigs } from '../game/board/ColorConfigs';
import { LevelMemeUI } from './LevelMemeUI';
const { ccclass, property } = _decorator;

@ccclass('ContentMemeUI')
export class ContentMemeUI extends Component {

    @property(Camera)
    public camera: Camera = null!;

    @property(Prefab)
    public memePrefab: Prefab = null!;

    @property(LevelMemeUI)
    levelMemeUI: LevelMemeUI = null!;

    private static _instance: ContentMemeUI | null = null;

    public static get Instance(): ContentMemeUI {
        if (!this._instance) {
            console.error("GameManager instance is not yet available.");
        }
        return this._instance!;
    }

    onLoad() {
        ContentMemeUI._instance = this;
    }

    public createMeme(pos: Vec3, color: ColorType) {
        const meme = instantiate(this.memePrefab);
        meme.setParent(this.node);
        meme.setScale(Vec3.ZERO);

        tween(meme)
            .to(0.5, { scale: Vec3.ONE }, { easing: 'backOut' })
            .start();

        const spr = ColorConfigs.Instance.getSpriteMemeFromColor(color);
        meme.getComponent(Sprite).spriteFrame = spr;

        // Thử cách khác: Sử dụng screenToWorld và convertToNodeSpaceAR
        const screenPos = new Vec3();
        this.camera.convertToUINode(pos, this.node, screenPos);

        meme.setPosition(screenPos);

        // getPosMemeGem đã trả về UI position rồi, không cần chuyển đổi
        const targetNode = this.levelMemeUI.getPosMemeGem(color);

        // Chuyển đổi position từ parent của target sang parent của meme (this.node)
        const targetWorldPos = new Vec3();
        targetNode.parent.getComponent(UITransform).convertToWorldSpaceAR(targetNode.position, targetWorldPos);

        const targetLocalPos = new Vec3();
        this.node.getComponent(UITransform).convertToNodeSpaceAR(targetWorldPos, targetLocalPos);

        tween(meme)
            .delay(0.5)
            .to(0.5, { position: targetLocalPos, scale: new Vec3(0.25, 0.25, 0.25) }, { easing: 'backIn' })
            .call(() => {
                this.levelMemeUI.updateAmout(color);
                meme.destroy();
            })
            .start();


    }
}




