import { _decorator, Component, Mask, math, Node, ParticleSystem2D, sp, Sprite, SpriteFrame, UITransform, Vec3, view } from 'cc';
import { UnlockCity } from '../../city/UnlockCity';
import { VerticalReveal } from '../../scene/VerticalReveal';
import { GameManager } from '../../core/GameManager';
import { GameState } from '../../enums/GameState';
const { ccclass, property } = _decorator;

@ccclass('GenerateMask')
export class GenerateMask extends Component {

    @property(SpriteFrame)
    private maskSprite: SpriteFrame = null;

    @property(Node)
    public fx_money: Node = null;

    private _revealMap: Map<Node, VerticalReveal[]> = new Map();

    private originalY: number = 0;

    public generateMask() {

        GameManager.Instance.onChangeState.on(this.onChangeState, this);
        this.originalY = this.fx_money.worldPosition.y;
        this._revealMap.clear();
        for (const child of this.node.children) {

            const unlockCity = child.getComponent(UnlockCity);

            if (unlockCity) {
                const revealsForChild: VerticalReveal[] = [];
                for (const fragment of unlockCity.node.children) {

                    const sprite = fragment.getComponent(Sprite);
                    const uiTransform = fragment.getComponent(UITransform);

                    const mask = new Node();
                    mask.layer = fragment.layer;
                    mask.setParent(fragment);
                    mask.name = 'Mask_' + fragment.name;

                    const maskSprite = mask.addComponent(Sprite);
                    maskSprite.spriteFrame = this.maskSprite;
                    maskSprite.sizeMode = Sprite.SizeMode.CUSTOM;

                    const maskUiTransform = mask.getComponent(UITransform);
                    maskUiTransform.width = uiTransform.width;
                    maskUiTransform.height = uiTransform.height;
                    maskUiTransform.anchorX = 0.5;
                    maskUiTransform.anchorY = 0;

                    const maskComp = mask.addComponent(Mask);
                    maskComp.type = Mask.Type.SPRITE_STENCIL;
                    maskComp.alphaThreshold = 0.1;

                    mask.setPosition(0, -uiTransform.height / 2);


                    const spriteNode = new Node();
                    spriteNode.setParent(mask);
                    spriteNode.layer = fragment.layer;

                    const spriteComp = spriteNode.addComponent(Sprite);
                    spriteComp.spriteFrame = sprite.spriteFrame;
                    spriteComp.sizeMode = Sprite.SizeMode.CUSTOM;

                    const spriteUiTransform = spriteNode.getComponent(UITransform);
                    spriteUiTransform.width = uiTransform.width;
                    spriteUiTransform.height = uiTransform.height;
                    spriteUiTransform.anchorX = 0.5;
                    spriteUiTransform.anchorY = 0.5;

                    spriteNode.setPosition(0, uiTransform.height / 2);


                    const verticalReveal = spriteNode.addComponent(VerticalReveal);
                    verticalReveal.targetSpriteNode = mask;
                    verticalReveal.setup();

                    revealsForChild.push(verticalReveal);
                }
                this._revealMap.set(child, revealsForChild);
            }
        }
    }

    public playReveal(cityNode: Node, index: number, callback: () => void) {
        const revealsForCity = this._revealMap.get(cityNode);
        if (!revealsForCity || !revealsForCity[index]) {
            callback?.();
            return;
        }

        revealsForCity[index].playReveal(callback);

        const frameSize = view.getFrameSize();

        const posY = frameSize.width > frameSize.height ? revealsForCity[index].node.worldPosition.y : Math.min(this.originalY, revealsForCity[index].node.worldPosition.y);
        const pos = new Vec3(revealsForCity[index].node.worldPosition.x, posY);

        this.fx_money.setWorldPosition(pos);
        this.fx_money.active = true;

        const particle = this.fx_money.children[0].getComponent(ParticleSystem2D);
        particle.resetSystem();
    }

    private onChangeState(state: GameState) {
        if (state === GameState.Win || state === GameState.Lose) {
            this.fx_money.active = false;
        }
    }
}


