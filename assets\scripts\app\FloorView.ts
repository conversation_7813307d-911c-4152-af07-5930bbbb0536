import { _decorator, Component, instantiate, Node, Prefab, Vec3 } from 'cc';
import { LevelDataConfig } from '../enums/LevelDataConfig';
import { BoardController } from './BoardController';
import { BlockManager } from './BlockManager';
const { ccclass, property } = _decorator;

@ccclass('FloorView')
export class FloorView extends Component {
    @property(Prefab)
    cellPrefab: Prefab = null;

    @property
    offsetZ: number = 0;

    public initialize(config: LevelDataConfig): void {

        // console.log("FloorView: initialize", config);
        this.node.destroyAllChildren();

        if (!this.cellPrefab) {
            console.log("FloorView: prefab is not assigned");
            return;
        }

        for (let indexRow = 0; indexRow < config.Row; indexRow++) {
            for (let indexCol = 0; indexCol < config.Column; indexCol++) {
                const cellViewObj = instantiate(this.cellPrefab);
                this.node.addChild(cellViewObj);
                const worldPos = BoardController.Instance.getCellWorldPosition(indexRow, indexCol);
                cellViewObj.setPosition(worldPos.x, this.offsetZ, worldPos.z);
                cellViewObj.name = `Cell (${indexRow},${indexCol})`;
            }
        }
        
    }
}







