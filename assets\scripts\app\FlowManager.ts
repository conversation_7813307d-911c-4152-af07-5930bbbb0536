import { _decorator, Component, Node, director } from 'cc';
import { Singleton } from '../common/Singleton';
import { SoundManager } from './SoundManager';  

const { ccclass, property } = _decorator;

const mraid = window['mraid'] || null;

@ccclass('FlowManager')
export class FlowManager  extends Component {


    private static _instance: FlowManager | null = null;

    private _loaded: boolean = false;

    public get Loaded(): boolean {
        return this._loaded;
    }

    public static get Instance(): FlowManager {
        if (!this._instance) {
            console.error("FlowManager instance is not yet available.");
        }
        return this._instance!;
    }

    protected onLoad(): void {
        if (FlowManager._instance) {
            this.node.destroy();
            return;
        }
        
        FlowManager._instance = this;
        director.addPersistRootNode(this.node);
        window['gameReady'] && window['gameReady']();
    }

    protected onDestroy(): void {
        if (FlowManager._instance === this) {
            FlowManager._instance = null;
        }
    }

    protected start(): void {
        this.waitSettingUp(window['advChannels']);
        window['gameReady'] && window['gameReady']();
        this.load();
    }

    private load(): void {
        this._loaded = true;
        SoundManager.Instance.playBgm(SoundManager.Instance.BGM);
    }  

    private waitSettingUp(ad_network): void {
        switch (ad_network) {
            case "Unity": {
                console.log('init case Unity');

                if (mraid) {
                    mraid.addEventListener('ready', () => {
                        console.log('mraid ready');
                    });
                }
                else {
                    console.log('mraid not found');
                }

                break;
            }
            case "Mintegral": {
                //call from window["gameStart"]
                console.log('init case Mintegral lately');
                break;
            }
            default: {
                console.log('init case Default');

                break;
            }
        }
    }

    public startGame(): void {

    }

}


window['advChannels'] = "{{__adv_channels_adapter__}}";

window["gameStart"] = function () {
    console.log('game start');
};
window["gameClose"] = function () {
    console.log('game close');
};