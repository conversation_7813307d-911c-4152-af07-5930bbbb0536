import { _decorator, Component, Node, Prefab, Vec3, math, MeshRenderer, instantiate, Texture2D } from 'cc';
const { ccclass, property } = _decorator;

interface Particle {
    node: Node;
    velocity: Vec3;
    lifeTimer: number;
}

@ccclass('EffectCube')
export class EffectCube extends Component {
    @property(Prefab) cubePrefab: Prefab | null = null;
    @property particleCount: number = 100;
    @property coneAngle: number = 30;
    @property minSpeed: number = 1;
    @property maxSpeed: number = 5;
    @property minSize: number = 0.1;
    @property maxSize: number = 0.5;
    @property burstCount: number = 10;
    @property minBurstDelay: number = 0.5;
    @property maxBurstDelay: number = 1.5;
    @property minLifetime: number = 2;
    @property maxLifetime: number = 5;
    @property loop: boolean = true;
    @property playOnAwake: boolean = true;
    @property(Texture2D) textureDefault: Texture2D | null = null;

    private particles: Particle[] = [];
    private burstTimer: number = 0;
    private isPlaying: boolean = false;
    private _texture: Texture2D | null = null;


    start(): void {
        if (!this.cubePrefab) {
            console.warn('Cube Prefab is not assigned to EffectCube.');
            return;
        }

        this.initializeParticlePool();
        this.burstTimer = 0;
        this.isPlaying = false;

        if (this.playOnAwake) {
            this.Play();
        }
    }

    private initializeParticlePool(): void {
        this.particles = [];

        for (let i = 0; i < this.particleCount; i++) {
            const cubeNode = instantiate(this.cubePrefab);
            if (!cubeNode) continue;

            cubeNode.active = false;
            this.node.addChild(cubeNode);

            this.particles.push({
                node: cubeNode,
                velocity: Vec3.ZERO.clone(),
                lifeTimer: 0
            });
        }
    }

    private emitBurst(): number {
        let emittedCount = 0;

        for (const particle of this.particles) {
            if (emittedCount >= this.burstCount) break;
            if (particle.node.active) continue;

            this.activateParticle(particle);
            emittedCount++;
        }

        return emittedCount;
    }

    private activateParticle(particle: Particle): void {
        particle.node.active = true;
        particle.lifeTimer = math.randomRange(this.minLifetime, this.maxLifetime);

        this.setParticleScale(particle);
        this.setParticlePosition(particle);
        this.setParticleVelocity(particle);
        this.setParticleTexture(particle);
    }

    private setParticleScale(particle: Particle): void {
        const scale = math.randomRange(this.minSize, this.maxSize);
        particle.node.setScale(scale, scale, scale);
    }

    private setParticlePosition(particle: Particle): void {
        particle.node.setPosition(Vec3.ZERO);
    }

    private setParticleVelocity(particle: Particle): void {
        const speed = math.randomRange(this.minSpeed, this.maxSpeed);
        const direction = this.generateRandomDirection();
        particle.velocity.set(direction.multiplyScalar(speed));
    }

    private generateRandomDirection(): Vec3 {
        const coneAngleRadians = math.toRadian(this.coneAngle / 2);
        const theta = math.randomRange(0, 2 * Math.PI);
        const phi = math.randomRange(0, coneAngleRadians);

        const direction = new Vec3(
            Math.cos(phi),
            Math.sin(phi) * Math.cos(theta),
            Math.sin(phi) * Math.sin(theta)
        );

        return direction.normalize();
    }

    private setParticleTexture(particle: Particle): void {
        const renderer = particle.node.getComponent(MeshRenderer);
        if (!renderer) return;

        const texture = this._texture || this.textureDefault;
        if (texture) {
            renderer.material.setProperty('mainTexture', texture);
        }
    }

    update(deltaTime: number): void {
        this.updateParticles(deltaTime);
        this.handleBurstEmission(deltaTime);
    }

    private updateParticles(deltaTime: number): void {
        for (const particle of this.particles) {
            if (!particle.node.active) continue;

            particle.lifeTimer -= deltaTime;

            if (particle.lifeTimer <= 0) {
                this.deactivateParticle(particle);
            } else {
                this.updateParticlePosition(particle, deltaTime);
            }
        }
    }

    private deactivateParticle(particle: Particle): void {
        particle.node.active = false;
        particle.node.setPosition(Vec3.ZERO);
    }

    private updateParticlePosition(particle: Particle, deltaTime: number): void {
        const displacement = particle.velocity.clone().multiplyScalar(deltaTime);
        const newPosition = particle.node.position.add(displacement);
        particle.node.setPosition(newPosition);
    }

    private handleBurstEmission(deltaTime: number): void {
        if (!this.isPlaying) return;

        this.burstTimer -= deltaTime;

        if (this.burstTimer <= 0) {
            const emittedCount = this.emitBurst();
            this.updateBurstTimer(emittedCount);
        }
    }

    private updateBurstTimer(emittedCount: number): void {
        if (this.loop) {
            this.burstTimer = math.randomRange(this.minBurstDelay, this.maxBurstDelay);
        } else {
            if (emittedCount > 0) {
                this.burstTimer = math.randomRange(this.minBurstDelay, this.maxBurstDelay);
            } else {
                this.isPlaying = false;
            }
        }
    }

    public setTexture(texture: Texture2D): void {
        this._texture = texture;
    }

    public Play(): void {
        this.isPlaying = true;
        this.burstTimer = 0;
    }

    public Stop(clearActiveParticles: boolean = false): void {
        this.isPlaying = false;

        if (clearActiveParticles) {
            this.clearAllParticles();
        }
    }

    private clearAllParticles(): void {
        for (const particle of this.particles) {
            if (particle.node.active) {
                particle.node.active = false;
                particle.node.setPosition(Vec3.ZERO);
                particle.lifeTimer = 0;
            }
        }
    }
}


