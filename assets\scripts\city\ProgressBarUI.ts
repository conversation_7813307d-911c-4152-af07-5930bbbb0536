import { _decorator, Component, Label, Sprite, tween, clamp01, lerp } from 'cc';
import { GameData } from './GameData';
import { GameManager } from '../core/GameManager';
import { CityDataConfig } from '../enums/LevelDataConfig';
import { SoundManager } from '../app/SoundManager';
import { GameState } from '../enums/GameState';
const { ccclass, property } = _decorator;

@ccclass('ProgressBarUI')
export class ProgressBarUI extends Component {
    private static _instance: ProgressBarUI | null = null;

    public static get Instance(): ProgressBarUI {
        return this._instance!;
    }

    @property(Sprite) private progressBar: Sprite = null!;
    @property(Label) private progressLabel: Label = null!;
    @property([CityDataConfig]) private cityDataConfig: CityDataConfig[] = [];
    @property({ tooltip: "Tốc độ animation của thanh tiến trình." })
    private animationSpeed: number = 5;

    private _targetProgress: number = 0;
    private _currentDisplayProgress: number = 0;
    private _currentValue: number = 0;
    private _maxValue: number = 0;
    private _isComplete: boolean = false;
    private readonly suffix: string = " Complete";

    protected onLoad(): void {
        ProgressBarUI._instance = this;
        this.initializeProgress();
    }
    
    protected start(): void {
        this.setupCityConfig();
        this.progressLabel.string = `0%${this.suffix}`;
    }

    private initializeProgress(): void {
        if (!this.progressBar) return;

        this.progressBar.fillRange = 0;
        this._targetProgress = 0;
        this._currentDisplayProgress = 0;
    }

    private setupCityConfig(): void {
        const cityID = GameData.instance.getSelectedCityID();
        const cityConfig = this.cityDataConfig.find(config => config.Type === cityID);

        if (cityConfig) {
            this.setMaxValue(cityConfig.maxElement);
        }
    }

    public setMaxValue(maxValue: number): void {
        this._maxValue = maxValue;
    }

    public updateCurrentValue(currentValue: number): void {
        this._currentValue += currentValue;
        this.updateProgress(this._currentValue, this._maxValue);

        SoundManager.Instance.playSfx(SoundManager.Instance.Star_Appear);
    }

    public updateProgress(currentValue: number, maxValue: number): void {
        if (!this.progressBar || maxValue <= 0) {
            this.resetProgress();
            return;
        }

        this._targetProgress = clamp01(currentValue / maxValue);
    }

    private resetProgress(): void {
        if (this.progressBar) {
            this.progressBar.fillRange = 0;
        }
        if (this.progressLabel) {
            this.progressLabel.string = "0%";
        }
    }

    private updateVisual(deltaTime: number): void {
        if (!this.progressBar) return;

        const previousProgress = this.progressBar.fillRange;
        const lerpSpeed = deltaTime * this.animationSpeed;

        this.progressBar.fillRange = lerp(this.progressBar.fillRange, this._targetProgress, lerpSpeed);
        this.updateProgressLabel(lerpSpeed);

        if (this.isAnimationComplete(previousProgress)) {
            this.handleAnimationComplete();
        }
    }

    private updateProgressLabel(lerpSpeed: number): void {
        if (!this.progressLabel) return;

        this._currentDisplayProgress = lerp(this._currentDisplayProgress, this._targetProgress, lerpSpeed);
        this.progressLabel.string = `${Math.floor(this._currentDisplayProgress * 100)}%${this.suffix}`;
    }

    private isAnimationComplete(previousProgress: number): boolean {
        return Math.abs(this.progressBar.fillRange - this._targetProgress) < 0.001 &&
               Math.abs(previousProgress - this._targetProgress) >= 0.001;
    }

    private handleAnimationComplete(): void {
        if (this._currentValue >= this._maxValue) {
            this._isComplete = true;
            this.progressLabel.string = `100%${this.suffix}`;

            tween(this.node)
                .delay(0.4)
                .call(() => GameManager.Instance.setGameState(GameState.Win))
                .start();
        }
    }

    update(deltaTime: number): void {
        if (!this._isComplete) {
            this.updateVisual(deltaTime);
        }
    }
}


