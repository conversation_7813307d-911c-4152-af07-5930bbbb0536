import { _decorator, Component, Node, <PERSON>p<PERSON>, <PERSON><PERSON>, Label, CCBoolean, tween, Vec3, easing } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('UnlockCity')
export class UnlockCity extends Component {

    @property(CCBoolean) private isUnlock: boolean = false;

    private _fragmentCity: Sprite[] = [];
    private _step: number = 0;

    onLoad() {
        this._fragmentCity = this.node.children.map(child => child.getComponent(Sprite));

        if (this.isUnlock) {
            this.UnlockAll();
        } else {
            this.LockAll();
        }
    }

    public UnlockPerFragment(step: number) {
        for (let i = 0; i < this._fragmentCity.length; i++) {
            this._fragmentCity[i].grayscale = step < i;
        }

        // Create one-time scale animation for the unlocked fragment
        if (step < this._fragmentCity.length) {
            const fragment = this._fragmentCity[step];
            const fragmentNode = fragment.node;

            // Stop any existing animations
            tween(fragmentNode).stop();

            // Create one-time scale animation with bounce easing
            tween(fragmentNode)
                .to(0.3, { scale: new Vec3(1.2, 1.2, 1.2) }, { easing: 'backInOut' })
                .to(0.3, { scale: new Vec3(1, 1, 1) }, { easing: 'quadOut' })
                .start();
        }
    }

    public getWorldPositionChildNode(index: number): Vec3 {
        return this._fragmentCity[index].node.getWorldPosition();
    }

    public UnlockAll() {
        for (let i = 0; i < this._fragmentCity.length; i++) {
            this._fragmentCity[i].grayscale = false;
            this._fragmentCity[i].node.scale = new Vec3(1, 1, 1);
        }
    }

    public LockAll() {
        for (let i = 0; i < this._fragmentCity.length; i++) {
            this._fragmentCity[i].grayscale = true;
            this._fragmentCity[i].node.scale = new Vec3(1, 1, 1);
        }
    }

    public setComplete(isComplete: boolean) {
        if (isComplete) {
            this.UnlockAll();
        } else {
            this.LockAll();
        }
    }
}


