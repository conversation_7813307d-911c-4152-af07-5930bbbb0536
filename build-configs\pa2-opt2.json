{"name": "pa2-opt2", "platform": "web-mobile", "buildPath": "project://build", "debug": false, "md5Cache": false, "skipCompressTexture": false, "sourceMaps": false, "polyfills": {"asyncFunctions": true, "coreJs": false}, "experimentalEraseModules": false, "useBuiltinServer": false, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": false, "packAutoAtlas": true, "startScene": "ac04ddc1-9d0a-4a69-9d8d-e22f20b219c1", "outputName": "web-mobile", "scenes": [{"url": "db://assets/scenes/pa2-opt2.scene", "uuid": "ac04ddc1-9d0a-4a69-9d8d-e22f20b219c1", "inBundle": false}, {"url": "db://assets/scenes/pa2-gameplay-opt2.scene", "uuid": "282b06df-a268-469f-9bef-d378bd45af28", "inBundle": false}], "web-mobile": [], "bundleConfigs": [], "packages": {"web-mobile": {"orientation": "auto", "embedWebDebugger": false, "cullEngineAsmJsModule": true, "__version__": "1.0.1"}, "cocos-service": {"configID": "0e26b4", "services": [], "__version__": "3.0.7"}}, "__version__": "1.3.5"}