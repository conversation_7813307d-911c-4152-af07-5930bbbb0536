import { _decorator, Component, Node, tween, UIOpacity, Vec3, sys } from 'cc';
import { GameManager } from '../core/GameManager';
import { GameState } from '../enums/GameState';
import { SoundManager } from '../app/SoundManager';
import { UIButton } from '../ui/UIButton';
const { ccclass, property } = _decorator;

@ccclass('EndScreenMeme')
export class EndScreenMeme extends Component {

    @property(UIOpacity) private uiOpacity: UIOpacity = null!;
    @property(Node) private content: Node = null!;
    @property(UIButton) private btnCTA: UIButton = null!;
    @property(UIButton) private btnCTAs: UIButton = null!;
    @property(UIButton) private btnTryAgain: UIButton = null!;
    @property(Node) private gameplay: Node = null!;

    start() {
        GameManager.Instance.onChangeState.on(this.onChangeState, this);
        this.node.active = false;

        this.btnCTA.InteractedEvent.on(() => {
            this.callCTA();
        }, this);

        this.btnCTAs.InteractedEvent.on(() => {
            this.callCTA();
        }, this);

        this.btnTryAgain.InteractedEvent.on(() => {
            this.callCTA();
        }, this);
    }

    private onChangeState(state: GameState) {
        if (state === GameState.Win) {
            this.show();
            SoundManager.Instance.playSfx(SoundManager.Instance.Win);

            console.log('win');

            this.gameplay.active = false;

            this.btnTryAgain.node.active = false;
            this.btnCTA.node.active = true;

        }
        if (state === GameState.Lose) {
            this.show();
            SoundManager.Instance.playSfx(SoundManager.Instance.Game_Over);
            console.log('lose');

            this.gameplay.active = false;

            this.btnTryAgain.node.active = true;
            this.btnCTA.node.active = false;
        }
    }

    public show() {

        const adNetwork = window['advChannels'];
        if (adNetwork === "Mintegral") {
            window['gameEnd']?.();
        }

        this.node.active = true
        this.content.setScale(Vec3.ZERO);

        tween(this.uiOpacity)
            .to(0.5, { opacity: 255 }, { easing: 'quartOut' })
            .start();

        tween(this.content)
            .to(0.5, { scale: Vec3.ONE }, { easing: 'quartOut' })
            .start();

        tween(this.node)
            .delay(1)
            .call(() => this.callCTA())
            .start();
    }

    public callCTA() {
        console.log('callCTA');
        const adNetwork = window['advChannels'];

        if (!adNetwork) return;

        switch (adNetwork) {
            case "Mintegral":
                window['install']?.();
                break;

            case "Unity":
            case "AppLovin":
                this.openAppStore();
                break;

            default:
                sys.openURL('');
                break;
        }
    }
    private openAppStore() {
        const isAndroid = sys.os === sys.OS.ANDROID;
        const storeFunction = isAndroid ?
            window["mraidOpenPlayStore"] :
            window["mraidOpenAppStore"];

        storeFunction?.();
    }
}


